"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Local = void 0;
const typeorm_1 = require("typeorm");
const empresa_entity_1 = require("./empresa.entity");
let Local = class Local {
    id;
    idEmpresa;
    empresa;
    descricao;
    endereco;
    numero;
    bairro;
    cep;
    telefone;
    razaoSocial;
    nomeFantasia;
    idCidade;
    complemento;
    dataHoraUsuarioInc;
    dataHoraUsuarioAlt;
    dataHoraUsuarioDel;
    usuarioInc;
    usuarioAlt;
    usuarioDel;
    isExcluido;
    uuid;
    dataSync;
    utilizaTabelaPreco;
    utilizaMobileCliente;
    trabalhaPedido;
    imprimePedido;
    utilizaOffline;
    cpfCnpj;
    emailComercial;
    latitude;
    longitude;
};
exports.Local = Local;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'ID' }),
    __metadata("design:type", Number)
], Local.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'EMPRESA_ID', nullable: false }),
    __metadata("design:type", Number)
], Local.prototype, "idEmpresa", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => empresa_entity_1.Empresa),
    (0, typeorm_1.JoinColumn)({ name: 'EMPRESA_ID' }),
    __metadata("design:type", empresa_entity_1.Empresa)
], Local.prototype, "empresa", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DESCRICAO', length: 100, nullable: false }),
    __metadata("design:type", String)
], Local.prototype, "descricao", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ENDERECO', length: 255, nullable: true }),
    __metadata("design:type", String)
], Local.prototype, "endereco", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'NUMERO', length: 20, nullable: true }),
    __metadata("design:type", String)
], Local.prototype, "numero", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'BAIRRO', length: 100, nullable: true }),
    __metadata("design:type", String)
], Local.prototype, "bairro", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CEP', length: 10, nullable: true }),
    __metadata("design:type", String)
], Local.prototype, "cep", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'TELEFONE', length: 20, nullable: true }),
    __metadata("design:type", String)
], Local.prototype, "telefone", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'RAZAO_SOCIAL', length: 255, nullable: true }),
    __metadata("design:type", String)
], Local.prototype, "razaoSocial", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'NOME_FANTASIA', length: 255, nullable: true }),
    __metadata("design:type", String)
], Local.prototype, "nomeFantasia", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CIDADE_ID', nullable: true }),
    __metadata("design:type", Number)
], Local.prototype, "idCidade", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'COMPLEMENTO', length: 100, nullable: true }),
    __metadata("design:type", String)
], Local.prototype, "complemento", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_HORA_USUARIO_INC', type: 'datetime', nullable: false }),
    __metadata("design:type", Date)
], Local.prototype, "dataHoraUsuarioInc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_HORA_USUARIO_ALT', type: 'datetime', nullable: false }),
    __metadata("design:type", Date)
], Local.prototype, "dataHoraUsuarioAlt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_HORA_USUARIO_DEL', type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], Local.prototype, "dataHoraUsuarioDel", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_INC', length: 500, nullable: false }),
    __metadata("design:type", String)
], Local.prototype, "usuarioInc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_ALT', length: 500, nullable: false }),
    __metadata("design:type", String)
], Local.prototype, "usuarioAlt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_DEL', length: 500, nullable: true }),
    __metadata("design:type", String)
], Local.prototype, "usuarioDel", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_EXCLUIDO', length: 1, nullable: false, default: 'N' }),
    __metadata("design:type", String)
], Local.prototype, "isExcluido", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'UUID', length: 50, nullable: true }),
    __metadata("design:type", String)
], Local.prototype, "uuid", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_SYNC', type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], Local.prototype, "dataSync", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'UTILIZA_TABELA_PRECO',
        type: 'bit',
        nullable: true,
        default: false,
    }),
    __metadata("design:type", Boolean)
], Local.prototype, "utilizaTabelaPreco", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'UTILIZA_MOBILE_CLIENTE',
        type: 'bit',
        nullable: true,
        default: false,
    }),
    __metadata("design:type", Boolean)
], Local.prototype, "utilizaMobileCliente", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'TRABALHA_PEDIDO',
        type: 'bit',
        nullable: true,
        default: false,
    }),
    __metadata("design:type", Boolean)
], Local.prototype, "trabalhaPedido", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'IMPRIME_PEDIDO',
        type: 'bit',
        nullable: true,
        default: false,
    }),
    __metadata("design:type", Boolean)
], Local.prototype, "imprimePedido", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'UTILIZA_OFFLINE',
        type: 'bit',
        nullable: true,
        default: false,
    }),
    __metadata("design:type", Boolean)
], Local.prototype, "utilizaOffline", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CPF_CNPJ', length: 20, nullable: true }),
    __metadata("design:type", String)
], Local.prototype, "cpfCnpj", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'EMAIL_COMERCIAL', length: 100, nullable: true }),
    __metadata("design:type", String)
], Local.prototype, "emailComercial", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LATITUDE', type: 'double', nullable: true }),
    __metadata("design:type", Number)
], Local.prototype, "latitude", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LONGITUDE', type: 'double', nullable: true }),
    __metadata("design:type", Number)
], Local.prototype, "longitude", void 0);
exports.Local = Local = __decorate([
    (0, typeorm_1.Entity)({ name: 'local' })
], Local);
//# sourceMappingURL=local.entity.js.map