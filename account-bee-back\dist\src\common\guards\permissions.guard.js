"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PermissionsGuard = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const user_roles_enum_1 = require("../enums/user-roles.enum");
const permissions_decorator_1 = require("../decorators/permissions.decorator");
let PermissionsGuard = class PermissionsGuard {
    reflector;
    constructor(reflector) {
        this.reflector = reflector;
    }
    canActivate(context) {
        const requiredPermissions = this.reflector.getAllAndOverride(permissions_decorator_1.PERMISSIONS_KEY, [context.getHandler(), context.getClass()]);
        if (!requiredPermissions) {
            return true;
        }
        const { user } = context.switchToHttp().getRequest();
        return this.hasRequiredPermissions(user, requiredPermissions);
    }
    hasRequiredPermissions(user, requiredPermissions) {
        const userPermissions = this.getUserPermissions(user);
        return requiredPermissions.every((permission) => userPermissions.includes(permission));
    }
    getUserPermissions(user) {
        const permissions = [];
        if (user.admGeral) {
            return Object.values(user_roles_enum_1.UserPermission);
        }
        if (user.admLocal) {
            permissions.push(user_roles_enum_1.UserPermission.USERS_READ, user_roles_enum_1.UserPermission.USERS_CREATE, user_roles_enum_1.UserPermission.USERS_UPDATE);
        }
        if (user.operador) {
            permissions.push(user_roles_enum_1.UserPermission.USERS_READ);
        }
        if (user.visualizaSaldosBancarios) {
            permissions.push(user_roles_enum_1.UserPermission.FINANCEIRO_VIEW_SALDOS);
        }
        if (user.gerenciarLctosFinanceiros) {
            permissions.push(user_roles_enum_1.UserPermission.FINANCEIRO_MANAGE_LANCAMENTOS);
        }
        if (user.relatoriosFinanceiros) {
            permissions.push(user_roles_enum_1.UserPermission.FINANCEIRO_REPORTS);
        }
        if (user.gerenciarProdutos) {
            permissions.push(user_roles_enum_1.UserPermission.PRODUTOS_MANAGE);
        }
        if (user.podeAcessarFiscal) {
            permissions.push(user_roles_enum_1.UserPermission.FISCAL_ACCESS);
        }
        if (user.podeAcessarConfiguracoes) {
            permissions.push(user_roles_enum_1.UserPermission.CONFIG_ACCESS);
        }
        if (user.relatoriosComandas) {
            permissions.push(user_roles_enum_1.UserPermission.COMANDAS_REPORTS);
        }
        return permissions;
    }
};
exports.PermissionsGuard = PermissionsGuard;
exports.PermissionsGuard = PermissionsGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [core_1.Reflector])
], PermissionsGuard);
//# sourceMappingURL=permissions.guard.js.map