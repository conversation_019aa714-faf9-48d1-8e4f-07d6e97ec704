"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IsCpfConstraint = void 0;
exports.IsCpf = IsCpf;
const class_validator_1 = require("class-validator");
let IsCpfConstraint = class IsCpfConstraint {
    validate(cpf, args) {
        if (!cpf)
            return true;
        const cleanCpf = cpf.replace(/\D/g, '');
        if (cleanCpf.length !== 11)
            return false;
        if (/^(\d)\1{10}$/.test(cleanCpf))
            return false;
        let sum = 0;
        for (let i = 0; i < 9; i++) {
            sum += parseInt(cleanCpf.charAt(i)) * (10 - i);
        }
        let digit1 = 11 - (sum % 11);
        if (digit1 === 10 || digit1 === 11)
            digit1 = 0;
        if (digit1 !== parseInt(cleanCpf.charAt(9)))
            return false;
        sum = 0;
        for (let i = 0; i < 10; i++) {
            sum += parseInt(cleanCpf.charAt(i)) * (11 - i);
        }
        let digit2 = 11 - (sum % 11);
        if (digit2 === 10 || digit2 === 11)
            digit2 = 0;
        if (digit2 !== parseInt(cleanCpf.charAt(10)))
            return false;
        return true;
    }
    defaultMessage(args) {
        return 'CPF inválido';
    }
};
exports.IsCpfConstraint = IsCpfConstraint;
exports.IsCpfConstraint = IsCpfConstraint = __decorate([
    (0, class_validator_1.ValidatorConstraint)({ async: false })
], IsCpfConstraint);
function IsCpf(validationOptions) {
    return function (object, propertyName) {
        (0, class_validator_1.registerDecorator)({
            target: object.constructor,
            propertyName: propertyName,
            options: validationOptions,
            constraints: [],
            validator: IsCpfConstraint,
        });
    };
}
//# sourceMappingURL=cpf.validator.js.map