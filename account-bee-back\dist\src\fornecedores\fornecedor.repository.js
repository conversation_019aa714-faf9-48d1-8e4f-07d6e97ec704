"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FornecedorRepository = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const fornecedor_entity_1 = require("../entities/fornecedor.entity");
let FornecedorRepository = class FornecedorRepository {
    fornecedorRepository;
    constructor(fornecedorRepository) {
        this.fornecedorRepository = fornecedorRepository;
    }
    async create(fornecedorData) {
        const fornecedor = this.fornecedorRepository.create(fornecedorData);
        return this.fornecedorRepository.save(fornecedor);
    }
    async findAll(empresaId, filterDto) {
        console.log('🔍 Repository - Buscando fornecedores para empresa:', empresaId);
        console.log('🔍 Repository - Filtros:', filterDto);
        const queryBuilder = this.createQueryBuilder();
        this.applyFilters(queryBuilder, filterDto, empresaId);
        this.applyDefaultSorting(queryBuilder);
        if (filterDto.page && filterDto.limit) {
            const skip = (filterDto.page - 1) * filterDto.limit;
            queryBuilder.skip(skip).take(filterDto.limit);
        }
        const [data, total] = await queryBuilder.getManyAndCount();
        console.log('✅ Repository - Encontrados', total, 'fornecedores');
        return { data, total };
    }
    async findById(id, empresaId) {
        console.log('🔍 Repository - Buscando fornecedor:', id, 'tipo:', typeof id, 'empresa:', empresaId);
        if (!id || isNaN(id) || id <= 0) {
            console.error('❌ Repository - ID inválido:', id);
            return null;
        }
        const queryBuilder = this.createQueryBuilder()
            .where('fornecedor.id = :id', { id })
            .andWhere('fornecedor.isExcluido = :isExcluido', { isExcluido: 'N' });
        if (empresaId) {
            queryBuilder.andWhere('fornecedor.empresaId = :empresaId', { empresaId });
        }
        const fornecedor = await queryBuilder.getOne();
        if (fornecedor) {
            console.log('✅ Repository - Fornecedor encontrado:', fornecedor.id);
        }
        else {
            console.log('⚠️ Repository - Fornecedor não encontrado');
        }
        return fornecedor;
    }
    async update(id, updateData) {
        await this.fornecedorRepository.update(id, updateData);
        const updatedFornecedor = await this.findById(id);
        if (!updatedFornecedor) {
            throw new Error('Fornecedor não encontrado após atualização');
        }
        return updatedFornecedor;
    }
    async delete(id, usuarioEmail) {
        const updateData = {
            isExcluido: 'S',
            dataHoraUsuarioDel: new Date(),
            usuarioDel: usuarioEmail,
            dataHoraUsuarioAlt: new Date(),
            usuarioAlt: usuarioEmail,
        };
        await this.fornecedorRepository.update(id, updateData);
    }
    async findByCpf(cpf, empresaId) {
        return this.fornecedorRepository.findOne({
            where: {
                cpf,
                empresaId,
                isExcluido: 'N',
            },
        });
    }
    async findByCnpj(cnpj, empresaId) {
        return this.fornecedorRepository.findOne({
            where: {
                cnpj,
                empresaId,
                isExcluido: 'N',
            },
        });
    }
    async findByDescricao(descricao, empresaId) {
        return this.fornecedorRepository.findOne({
            where: {
                descricao,
                empresaId,
                isExcluido: 'N',
            },
        });
    }
    createQueryBuilder() {
        return this.fornecedorRepository.createQueryBuilder('fornecedor');
    }
    applyFilters(queryBuilder, filterDto, empresaId) {
        queryBuilder.where('fornecedor.empresaId = :empresaId', { empresaId });
        const status = filterDto.status || 'ativo';
        if (status === 'ativo') {
            queryBuilder.andWhere('fornecedor.isExcluido = :isExcluido', { isExcluido: 'N' });
        }
        else if (status === 'excluido') {
            queryBuilder.andWhere('fornecedor.isExcluido = :isExcluido', { isExcluido: 'S' });
        }
        if (filterDto.nome) {
            queryBuilder.andWhere('fornecedor.descricao LIKE :nome', {
                nome: `%${filterDto.nome}%`,
            });
        }
        if (filterDto.cpf) {
            queryBuilder.andWhere('fornecedor.cpf = :cpf', {
                cpf: filterDto.cpf,
            });
        }
        if (filterDto.cnpj) {
            queryBuilder.andWhere('fornecedor.cnpj = :cnpj', {
                cnpj: filterDto.cnpj,
            });
        }
        if (filterDto.uuid) {
            queryBuilder.andWhere('fornecedor.uuid = :uuid', {
                uuid: filterDto.uuid,
            });
        }
    }
    applyDefaultSorting(queryBuilder) {
        queryBuilder
            .orderBy('fornecedor.dataHoraUsuarioInc', 'DESC')
            .addOrderBy('fornecedor.dataHoraUsuarioAlt', 'DESC')
            .addOrderBy('fornecedor.id', 'DESC');
    }
    async findAllByEmpresa(empresaId) {
        return this.fornecedorRepository.find({
            where: {
                empresaId,
                isExcluido: 'N',
            },
            order: {
                descricao: 'ASC',
            },
        });
    }
};
exports.FornecedorRepository = FornecedorRepository;
exports.FornecedorRepository = FornecedorRepository = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(fornecedor_entity_1.Fornecedor)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], FornecedorRepository);
//# sourceMappingURL=fornecedor.repository.js.map