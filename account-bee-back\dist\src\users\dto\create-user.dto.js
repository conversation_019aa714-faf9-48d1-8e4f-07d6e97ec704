"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateUserDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateUserDto {
    nomeUsuario;
    email;
    senha;
    telefone;
    empresaId;
    admGeral;
    admLocal;
    operador;
    operadorEstoque;
    visualizaSaldosBancarios;
    gerenciarLctosFinanceiros;
    relatoriosFinanceiros;
    relatoriosComandas;
    gerenciarProdutos;
    podeAcessarFiscal;
    podeAcessarConfiguracoes;
    twoAuthentication;
    podeVisualizarCatFinOculto;
    desconectarUsuarios;
    podeGerarExcelFinanceiro;
    podeImprimirLancamentosFinanceiro;
}
exports.CreateUserDto = CreateUserDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome completo do usuário',
        example: 'João Silva',
        minLength: 2,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Nome é obrigatório' }),
    (0, class_validator_1.IsString)({ message: 'Nome deve ser uma string' }),
    (0, class_validator_1.MinLength)(2, { message: 'Nome deve ter no mínimo 2 caracteres' }),
    __metadata("design:type", String)
], CreateUserDto.prototype, "nomeUsuario", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Email do usuário',
        example: '<EMAIL>',
        uniqueItems: true,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Email é obrigatório' }),
    (0, class_validator_1.IsEmail)({}, { message: 'Email inválido' }),
    __metadata("design:type", String)
], CreateUserDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Senha do usuário',
        example: 'senhaSegura123',
        minLength: 6,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Senha é obrigatória' }),
    (0, class_validator_1.IsString)({ message: 'Senha deve ser uma string' }),
    (0, class_validator_1.MinLength)(6, { message: 'Senha deve ter no mínimo 6 caracteres' }),
    __metadata("design:type", String)
], CreateUserDto.prototype, "senha", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Telefone do usuário',
        example: '(11) 98765-4321',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Telefone deve ser uma string' }),
    (0, class_validator_1.Matches)(/^\(\d{2}\)\s?\d{4,5}-?\d{4}$/, {
        message: 'Telefone deve estar no formato (XX) XXXXX-XXXX ou (XX) XXXX-XXXX',
    }),
    __metadata("design:type", String)
], CreateUserDto.prototype, "telefone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da empresa à qual o usuário pertence (opcional, será inferida do usuário logado se não fornecida)',
        example: 1,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'ID da empresa deve ser um número' }),
    __metadata("design:type", Number)
], CreateUserDto.prototype, "empresaId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Define se o usuário é administrador geral',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'admGeral deve ser um boolean' }),
    __metadata("design:type", Boolean)
], CreateUserDto.prototype, "admGeral", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Define se o usuário é administrador local',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'admLocal deve ser um boolean' }),
    __metadata("design:type", Boolean)
], CreateUserDto.prototype, "admLocal", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Define se o usuário é operador',
        example: true,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'operador deve ser um boolean' }),
    __metadata("design:type", Boolean)
], CreateUserDto.prototype, "operador", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Define se o usuário é operador de estoque',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'operadorEstoque deve ser um boolean' }),
    __metadata("design:type", Boolean)
], CreateUserDto.prototype, "operadorEstoque", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permite visualizar saldos bancários',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'visualizaSaldosBancarios deve ser um boolean' }),
    __metadata("design:type", Boolean)
], CreateUserDto.prototype, "visualizaSaldosBancarios", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permite gerenciar lançamentos financeiros',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'gerenciarLctosFinanceiros deve ser um boolean' }),
    __metadata("design:type", Boolean)
], CreateUserDto.prototype, "gerenciarLctosFinanceiros", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permite acessar relatórios financeiros',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'relatoriosFinanceiros deve ser um boolean' }),
    __metadata("design:type", Boolean)
], CreateUserDto.prototype, "relatoriosFinanceiros", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permite acessar relatórios de comandas',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'relatoriosComandas deve ser um boolean' }),
    __metadata("design:type", Boolean)
], CreateUserDto.prototype, "relatoriosComandas", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permite gerenciar produtos',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'gerenciarProdutos deve ser um boolean' }),
    __metadata("design:type", Boolean)
], CreateUserDto.prototype, "gerenciarProdutos", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permite acessar módulo fiscal',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'podeAcessarFiscal deve ser um boolean' }),
    __metadata("design:type", Boolean)
], CreateUserDto.prototype, "podeAcessarFiscal", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permite acessar configurações',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'podeAcessarConfiguracoes deve ser um boolean' }),
    __metadata("design:type", Boolean)
], CreateUserDto.prototype, "podeAcessarConfiguracoes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Define se habilita autenticação em dois fatores',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'twoAuthentication deve ser um boolean' }),
    __metadata("design:type", Boolean)
], CreateUserDto.prototype, "twoAuthentication", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permite visualizar categorias financeiras ocultas',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'podeVisualizarCatFinOculto deve ser um boolean' }),
    __metadata("design:type", Boolean)
], CreateUserDto.prototype, "podeVisualizarCatFinOculto", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permite desconectar outros usuários',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'desconectarUsuarios deve ser um boolean' }),
    __metadata("design:type", Boolean)
], CreateUserDto.prototype, "desconectarUsuarios", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permite gerar arquivos Excel de relatórios financeiros',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'podeGerarExcelFinanceiro deve ser um boolean' }),
    __metadata("design:type", Boolean)
], CreateUserDto.prototype, "podeGerarExcelFinanceiro", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permite imprimir lançamentos financeiros',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({
        message: 'podeImprimirLancamentosFinanceiro deve ser um boolean',
    }),
    __metadata("design:type", Boolean)
], CreateUserDto.prototype, "podeImprimirLancamentosFinanceiro", void 0);
//# sourceMappingURL=create-user.dto.js.map