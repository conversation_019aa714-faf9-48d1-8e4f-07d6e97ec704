import { Repository } from 'typeorm';
import { Usuario } from '../entities/usuario.entity';
import { Empresa } from '../entities/empresa.entity';
import { Local } from '../entities/local.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UpdatePasswordDto } from './dto/update-password.dto';
import { FilterUsersDto } from './dto/filter-users.dto';
import { LegacyCryptoService } from '../crypto/legacy-crypto.service';
import { UserResponseDto } from './dto/user-response.dto';
import { BaseService } from '../common/services/base.service';
export declare class UsersService extends BaseService {
    private readonly usuarioRepository;
    private readonly empresaRepository;
    private readonly localRepository;
    private readonly cryptoService;
    constructor(usuarioRepository: Repository<Usuario>, empresaRepository: Repository<Empresa>, localRepository: Repository<Local>, cryptoService: LegacyCryptoService);
    create(createUserDto: CreateUserDto, currentUser: Usuario): Promise<UserResponseDto>;
    findAll(filterDto: FilterUsersDto, currentUser: Usuario): Promise<{
        data: UserResponseDto[];
        total: number;
        page: number;
        limit: number;
    }>;
    findOne(id: number, currentUser: Usuario): Promise<UserResponseDto>;
    update(id: number, updateUserDto: UpdateUserDto, currentUser: Usuario): Promise<UserResponseDto>;
    updatePassword(id: number, updatePasswordDto: UpdatePasswordDto, currentUser: Usuario): Promise<void>;
    remove(id: number, currentUser: Usuario): Promise<void>;
    toggleStatus(id: number, currentUser: Usuario): Promise<UserResponseDto>;
    findByEmpresa(empresaId: number, currentUser: Usuario): Promise<UserResponseDto[]>;
    private prepareUserResponse;
}
