import { Repository } from 'typeorm';
import { PlanoConta } from '../entities/plano-conta.entity';
import { PlanoContaDropdownDto } from '../lancamentos-financeiros/dto';
export declare class PlanoContasService {
    private readonly planoContaRepository;
    constructor(planoContaRepository: Repository<PlanoConta>);
    findAllByEmpresa(empresaId: number): Promise<PlanoContaDropdownDto[]>;
    findById(id: number, empresaId: number): Promise<PlanoConta | null>;
}
