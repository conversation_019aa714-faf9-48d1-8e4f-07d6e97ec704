"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateLancamentoFinanceiroDto = exports.CostCenterAllocationDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class CostCenterAllocationDto {
    centroCustoId;
    valor;
    porcentagem;
}
exports.CostCenterAllocationDto = CostCenterAllocationDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CostCenterAllocationDto.prototype, "centroCustoId", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CostCenterAllocationDto.prototype, "valor", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], CostCenterAllocationDto.prototype, "porcentagem", void 0);
class CreateLancamentoFinanceiroDto {
    pessoaId;
    descricao;
    valor;
    dataLancamento;
    dataCompetencia;
    contaId;
    localId;
    valorBruto;
    categoriaLctoFinanceiroId;
    alocacoesCentroCusto;
    planoContaCredito;
    fornecedorId;
    observacao;
    repetirReceita;
    tipoRepeticao;
    periodicidade;
    quantidadeRepeticoes;
    empresaId;
    tipoLancamentoFinanceiroId;
}
exports.CreateLancamentoFinanceiroDto = CreateLancamentoFinanceiroDto;
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'Cliente/Pessoa é obrigatório' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateLancamentoFinanceiroDto.prototype, "pessoaId", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'Descrição é obrigatória' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateLancamentoFinanceiroDto.prototype, "descricao", void 0);
__decorate([
    (0, class_validator_1.IsNotEmpty)({ message: 'Valor líquido é obrigatório' }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0.01, { message: 'Valor líquido deve ser maior que zero' }),
    __metadata("design:type", Number)
], CreateLancamentoFinanceiroDto.prototype, "valor", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateLancamentoFinanceiroDto.prototype, "dataLancamento", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateLancamentoFinanceiroDto.prototype, "dataCompetencia", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateLancamentoFinanceiroDto.prototype, "contaId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateLancamentoFinanceiroDto.prototype, "localId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateLancamentoFinanceiroDto.prototype, "valorBruto", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateLancamentoFinanceiroDto.prototype, "categoriaLctoFinanceiroId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CostCenterAllocationDto),
    __metadata("design:type", Array)
], CreateLancamentoFinanceiroDto.prototype, "alocacoesCentroCusto", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateLancamentoFinanceiroDto.prototype, "planoContaCredito", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateLancamentoFinanceiroDto.prototype, "fornecedorId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateLancamentoFinanceiroDto.prototype, "observacao", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    (0, class_transformer_1.Transform)(({ value }) => {
        if (typeof value === 'string') {
            return value === 'true' || value === '1';
        }
        return Boolean(value);
    }),
    __metadata("design:type", Boolean)
], CreateLancamentoFinanceiroDto.prototype, "repetirReceita", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateLancamentoFinanceiroDto.prototype, "tipoRepeticao", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateLancamentoFinanceiroDto.prototype, "periodicidade", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CreateLancamentoFinanceiroDto.prototype, "quantidadeRepeticoes", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateLancamentoFinanceiroDto.prototype, "empresaId", void 0);
__decorate([
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateLancamentoFinanceiroDto.prototype, "tipoLancamentoFinanceiroId", void 0);
//# sourceMappingURL=create-lancamento-financeiro.dto.js.map