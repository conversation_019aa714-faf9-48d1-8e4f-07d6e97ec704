import { LancamentosFinanceirosService } from './lancamentos-financeiros.service';
import { CreateLancamentoFinanceiroDto, LancamentoFinanceiroResponseDto, CostCenterAllocationDto } from './dto';
export declare class LancamentosFinanceirosController {
    private readonly lancamentosService;
    constructor(lancamentosService: LancamentosFinanceirosService);
    createReceita(createDto: CreateLancamentoFinanceiroDto, req: any): Promise<LancamentoFinanceiroResponseDto>;
    findOne(id: string, req: any): Promise<LancamentoFinanceiroResponseDto>;
    updateCostCenterAllocations(id: string, allocations: CostCenterAllocationDto[], req: any): Promise<void>;
    getCostCenterAllocations(id: string, req: any): Promise<any[]>;
    deleteCostCenterAllocation(lancamentoId: string, allocationId: string, req: any): Promise<void>;
}
