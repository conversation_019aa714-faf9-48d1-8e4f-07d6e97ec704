import { useState, useEffect, useMemo } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useApiClient } from '@/utils/apiClient';

export interface FinancialTransaction {
  id: number;
  descricao: string;
  dataLancamento?: Date;
  dataCompetencia?: Date;
  valor: number;
  valorBruto?: number;
  observacao?: string;
  efetivado?: boolean;
  conciliado?: boolean;
  tipoLancamentoFinanceiroId: number;
  pessoa?: {
    id: number;
    nome: string;
  };
  conta?: {
    id: number;
    descricao: string;
  };
  local?: {
    id: number;
    nome: string;
  };
  categoriaLctoFinanceiro?: {
    id: number;
    descricao: string;
  };
  tipoLancamentoFinanceiro?: {
    id: number;
    descricao: string;
  };
}

export interface FinancialSummary {
  totalReceitas: number;
  totalDespesas: number;
  saldo: number;
  quantidadeReceitas: number;
  quantidadeDespesas: number;
}

export interface FinancialFilters {
  tipo?: number; // 1=Receita, 2=Despesa
  page?: number;
  limit?: number;
  dataInicio?: string;
  dataFim?: string;
}

export interface FinancialDataResponse {
  data: FinancialTransaction[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export function useFinancialData() {
  const [transactions, setTransactions] = useState<FinancialTransaction[]>([]);
  const [summary, setSummary] = useState<FinancialSummary>({
    totalReceitas: 0,
    totalDespesas: 0,
    saldo: 0,
    quantidadeReceitas: 0,
    quantidadeDespesas: 0,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });

  // Estados para controlar quando os dados foram carregados
  const [receitasLoaded, setReceitasLoaded] = useState(false);
  const [despesasLoaded, setDespesasLoaded] = useState(false);
  const [currentType, setCurrentType] = useState<'receitas' | 'despesas' | null>(null);

  const { token } = useAuth();

  // Memoize o apiClient para evitar recriações desnecessárias
  const apiClient = useMemo(() => {
    return useApiClient(token);
  }, [token]);

  // Buscar transações financeiras
  const fetchTransactions = async (filters: FinancialFilters = {}) => {
    if (!token || !apiClient) {
      console.error('❌ Token ou apiClient não encontrado');
      throw new Error('Token de autenticação não encontrado');
    }

    console.log('🔄 Iniciando busca de transações com filtros:', filters);
    setLoading(true);
    setError(null);

    try {
      const queryParams = new URLSearchParams();

      if (filters.tipo) queryParams.append('tipo', filters.tipo.toString());
      if (filters.page) queryParams.append('page', filters.page.toString());
      if (filters.limit) queryParams.append('limit', filters.limit.toString());
      if (filters.dataInicio) queryParams.append('dataInicio', filters.dataInicio);
      if (filters.dataFim) queryParams.append('dataFim', filters.dataFim);

      const url = `/lancamentos-financeiros${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      console.log('📡 Fazendo requisição para:', url);

      const response = await apiClient.call(url);
      console.log('📡 Resposta recebida:', response.status, response.statusText);

      if (response.ok) {
        const data: FinancialDataResponse = await response.json();
        console.log('✅ Dados recebidos:', data);

        setTransactions(data.data || []);
        setPagination({
          page: data.page || 1,
          limit: data.limit || 10,
          total: data.total || 0,
          totalPages: data.totalPages || 0,
        });
        return data;
      } else {
        const errorData = await response.json().catch(() => ({}));
        console.error('❌ Erro na resposta:', response.status, errorData);
        throw new Error(errorData.message || `Erro ${response.status}: ${response.statusText}`);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido ao buscar transações';
      console.error('❌ Erro ao buscar transações:', err);
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Buscar resumo financeiro
  const fetchSummary = async (filters: { dataInicio?: string; dataFim?: string } = {}) => {
    if (!token || !apiClient) {
      throw new Error('Token de autenticação não encontrado');
    }

    try {
      const queryParams = new URLSearchParams();
      
      if (filters.dataInicio) queryParams.append('dataInicio', filters.dataInicio);
      if (filters.dataFim) queryParams.append('dataFim', filters.dataFim);

      const url = `/lancamentos-financeiros/resumo${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      
      const response = await apiClient.call(url);

      if (response.ok) {
        const data: FinancialSummary = await response.json();
        setSummary(data);
        return data;
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Erro ao buscar resumo financeiro');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido ao buscar resumo';
      console.error('Erro ao buscar resumo financeiro:', errorMessage);
      throw err;
    }
  };

  // Buscar receitas (apenas se não foram carregadas)
  const fetchReceitas = async (filters: Omit<FinancialFilters, 'tipo'> = {}, forceReload = false) => {
    if (!forceReload && receitasLoaded && currentType === 'receitas') {
      console.log('📊 Receitas já carregadas, usando cache');
      return { data: transactions, total: pagination.total, page: pagination.page, limit: pagination.limit, totalPages: pagination.totalPages };
    }

    console.log('🔄 Carregando receitas do servidor...');
    const result = await fetchTransactions({ ...filters, tipo: 1 });
    setReceitasLoaded(true);
    setCurrentType('receitas');
    return result;
  };

  // Buscar despesas (apenas se não foram carregadas)
  const fetchDespesas = async (filters: Omit<FinancialFilters, 'tipo'> = {}, forceReload = false) => {
    if (!forceReload && despesasLoaded && currentType === 'despesas') {
      console.log('📊 Despesas já carregadas, usando cache');
      return { data: transactions, total: pagination.total, page: pagination.page, limit: pagination.limit, totalPages: pagination.totalPages };
    }

    console.log('🔄 Carregando despesas do servidor...');
    const result = await fetchTransactions({ ...filters, tipo: 2 });
    setDespesasLoaded(true);
    setCurrentType('despesas');
    return result;
  };

  // Recarregar dados (força nova consulta)
  const refreshData = async (tipo: 'receitas' | 'despesas' | 'both' = 'both', filters: FinancialFilters = {}) => {
    console.log('🔄 Forçando recarga dos dados...');

    if (tipo === 'receitas' || tipo === 'both') {
      setReceitasLoaded(false);
      await fetchReceitas(filters, true);
    }

    if (tipo === 'despesas' || tipo === 'both') {
      setDespesasLoaded(false);
      await fetchDespesas(filters, true);
    }

    await fetchSummary({ dataInicio: filters.dataInicio, dataFim: filters.dataFim });
  };

  // Limpar cache
  const clearCache = () => {
    console.log('🗑️ Limpando cache dos dados financeiros...');
    setReceitasLoaded(false);
    setDespesasLoaded(false);
    setCurrentType(null);
    setTransactions([]);
    setSummary({
      totalReceitas: 0,
      totalDespesas: 0,
      saldo: 0,
      quantidadeReceitas: 0,
      quantidadeDespesas: 0,
    });
  };

  return {
    // Estados
    transactions,
    summary,
    loading,
    error,
    pagination,

    // Estados de controle
    receitasLoaded,
    despesasLoaded,
    currentType,

    // Métodos
    fetchTransactions,
    fetchReceitas,
    fetchDespesas,
    fetchSummary,
    refreshData,
    clearCache,
  };
}
