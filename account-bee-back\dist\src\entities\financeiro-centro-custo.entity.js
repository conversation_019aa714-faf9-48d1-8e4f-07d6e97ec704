"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinanceiroCentroCusto = void 0;
const typeorm_1 = require("typeorm");
const centro_custo_entity_1 = require("./centro-custo.entity");
const lancamento_financeiro_entity_1 = require("./lancamento-financeiro.entity");
let FinanceiroCentroCusto = class FinanceiroCentroCusto {
    id;
    valor;
    porcentagem;
    centroCustoId;
    lancamentoFinanceiroId;
    centroCusto;
    lancamentoFinanceiro;
    dataHoraUsuarioAlt;
    dataHoraUsuarioDel;
    dataHoraUsuarioInc;
    dataSync;
    isExcluido;
    usuarioAlt;
    usuarioDel;
    usuarioInc;
    uuid;
};
exports.FinanceiroCentroCusto = FinanceiroCentroCusto;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'ID' }),
    __metadata("design:type", Number)
], FinanceiroCentroCusto.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'VALOR', length: 500, nullable: false }),
    __metadata("design:type", String)
], FinanceiroCentroCusto.prototype, "valor", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'PORCENTAGEM', type: 'decimal', precision: 10, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], FinanceiroCentroCusto.prototype, "porcentagem", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CENTRO_CUSTO_ID', nullable: false }),
    __metadata("design:type", Number)
], FinanceiroCentroCusto.prototype, "centroCustoId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LANCAMENTO_FINANCEIRO_ID', nullable: false }),
    __metadata("design:type", Number)
], FinanceiroCentroCusto.prototype, "lancamentoFinanceiroId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => centro_custo_entity_1.CentroCusto),
    (0, typeorm_1.JoinColumn)({ name: 'CENTRO_CUSTO_ID' }),
    __metadata("design:type", centro_custo_entity_1.CentroCusto)
], FinanceiroCentroCusto.prototype, "centroCusto", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => lancamento_financeiro_entity_1.LancamentoFinanceiro, lancamento => lancamento.financeiroCentroCustos),
    (0, typeorm_1.JoinColumn)({ name: 'LANCAMENTO_FINANCEIRO_ID' }),
    __metadata("design:type", lancamento_financeiro_entity_1.LancamentoFinanceiro)
], FinanceiroCentroCusto.prototype, "lancamentoFinanceiro", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'DATA_HORA_USUARIO_ALT',
        type: 'datetime',
        precision: 6,
        nullable: false,
    }),
    __metadata("design:type", Date)
], FinanceiroCentroCusto.prototype, "dataHoraUsuarioAlt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'DATA_HORA_USUARIO_DEL',
        type: 'datetime',
        precision: 6,
        nullable: true,
    }),
    __metadata("design:type", Date)
], FinanceiroCentroCusto.prototype, "dataHoraUsuarioDel", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'DATA_HORA_USUARIO_INC',
        type: 'datetime',
        precision: 6,
        nullable: false,
    }),
    __metadata("design:type", Date)
], FinanceiroCentroCusto.prototype, "dataHoraUsuarioInc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_SYNC', type: 'datetime', precision: 6, nullable: true }),
    __metadata("design:type", Date)
], FinanceiroCentroCusto.prototype, "dataSync", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_EXCLUIDO', length: 1, nullable: false, default: 'N' }),
    __metadata("design:type", String)
], FinanceiroCentroCusto.prototype, "isExcluido", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_ALT', length: 500, nullable: false }),
    __metadata("design:type", String)
], FinanceiroCentroCusto.prototype, "usuarioAlt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_DEL', length: 500, nullable: true }),
    __metadata("design:type", String)
], FinanceiroCentroCusto.prototype, "usuarioDel", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_INC', length: 500, nullable: false }),
    __metadata("design:type", String)
], FinanceiroCentroCusto.prototype, "usuarioInc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'UUID', length: 50, nullable: true }),
    __metadata("design:type", String)
], FinanceiroCentroCusto.prototype, "uuid", void 0);
exports.FinanceiroCentroCusto = FinanceiroCentroCusto = __decorate([
    (0, typeorm_1.Entity)({ name: 'FINANCEIRO_CENTRO_CUSTO' })
], FinanceiroCentroCusto);
//# sourceMappingURL=financeiro-centro-custo.entity.js.map