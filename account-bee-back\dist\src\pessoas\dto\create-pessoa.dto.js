"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePessoaDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const cpf_validator_1 = require("../../common/validators/cpf.validator");
const cnpj_validator_1 = require("../../common/validators/cnpj.validator");
class CreatePessoaDto {
    nome;
    dataNascimento;
    email;
    cpf;
    cnpj;
    cep;
    endereco;
    telefone;
    observacoes;
    empresaId;
    uuid;
}
exports.CreatePessoaDto = CreatePessoaDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome completo da pessoa',
        example: 'João Silva Santos',
        maxLength: 255,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Nome é obrigatório' }),
    (0, class_validator_1.IsString)({ message: 'Nome deve ser uma string' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Nome deve ter no máximo 255 caracteres' }),
    __metadata("design:type", String)
], CreatePessoaDto.prototype, "nome", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de nascimento da pessoa',
        example: '1990-05-15',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'Data de nascimento deve ser uma data válida (YYYY-MM-DD)' }),
    __metadata("design:type", String)
], CreatePessoaDto.prototype, "dataNascimento", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Email da pessoa',
        example: '<EMAIL>',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEmail)({}, { message: 'Email inválido' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Email deve ter no máximo 255 caracteres' }),
    __metadata("design:type", String)
], CreatePessoaDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'CPF da pessoa (apenas números ou formatado)',
        example: '12345678901',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'CPF deve ser uma string' }),
    (0, cpf_validator_1.IsCpf)({ message: 'CPF inválido' }),
    __metadata("design:type", String)
], CreatePessoaDto.prototype, "cpf", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'CNPJ da pessoa (apenas números ou formatado)',
        example: '12345678000190',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'CNPJ deve ser uma string' }),
    (0, cnpj_validator_1.IsCnpj)({ message: 'CNPJ inválido' }),
    __metadata("design:type", String)
], CreatePessoaDto.prototype, "cnpj", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'CEP da pessoa',
        example: '12345-678',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'CEP deve ser uma string' }),
    (0, class_validator_1.Matches)(/^\d{5}-?\d{3}$/, {
        message: 'CEP deve estar no formato 12345-678 ou 12345678',
    }),
    (0, class_validator_1.MaxLength)(20, { message: 'CEP deve ter no máximo 20 caracteres' }),
    __metadata("design:type", String)
], CreatePessoaDto.prototype, "cep", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Endereço completo da pessoa',
        example: 'Rua das Flores, 123, Bairro Centro, Cidade - Estado',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Endereço deve ser uma string' }),
    __metadata("design:type", String)
], CreatePessoaDto.prototype, "endereco", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Telefone da pessoa',
        example: '(11) 98765-4321',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Telefone deve ser uma string' }),
    (0, class_validator_1.Matches)(/^\(\d{2}\)\s?\d{4,5}-?\d{4}$/, {
        message: 'Telefone deve estar no formato (XX) XXXXX-XXXX ou (XX) XXXX-XXXX',
    }),
    (0, class_validator_1.MaxLength)(50, { message: 'Telefone deve ter no máximo 50 caracteres' }),
    __metadata("design:type", String)
], CreatePessoaDto.prototype, "telefone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Observações sobre a pessoa',
        example: 'Cliente preferencial, sempre pontual nos pagamentos',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Observações devem ser uma string' }),
    __metadata("design:type", String)
], CreatePessoaDto.prototype, "observacoes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da empresa à qual a pessoa pertence (opcional, será inferida do usuário logado se não fornecida)',
        example: 1,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'ID da empresa deve ser um número' }),
    __metadata("design:type", Number)
], CreatePessoaDto.prototype, "empresaId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'UUID único da pessoa',
        example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'UUID deve ser uma string' }),
    (0, class_validator_1.MaxLength)(50, { message: 'UUID deve ter no máximo 50 caracteres' }),
    __metadata("design:type", String)
], CreatePessoaDto.prototype, "uuid", void 0);
//# sourceMappingURL=create-pessoa.dto.js.map