"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CentroCustoModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const centro_custo_service_1 = require("./centro-custo.service");
const centro_custo_controller_1 = require("./centro-custo.controller");
const centro_custo_entity_1 = require("../entities/centro-custo.entity");
let CentroCustoModule = class CentroCustoModule {
};
exports.CentroCustoModule = CentroCustoModule;
exports.CentroCustoModule = CentroCustoModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([centro_custo_entity_1.CentroCusto])],
        controllers: [centro_custo_controller_1.CentroCustoController],
        providers: [centro_custo_service_1.CentroCustoService],
        exports: [centro_custo_service_1.CentroCustoService],
    })
], CentroCustoModule);
//# sourceMappingURL=centro-custo.module.js.map