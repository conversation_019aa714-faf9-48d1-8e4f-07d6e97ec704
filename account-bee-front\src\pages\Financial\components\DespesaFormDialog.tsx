import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/shadcn/dialog';
import { Button } from '@/components/ui/shadcn/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/shadcn/form';
import { Input } from '@/components/ui/shadcn/input';
import { Textarea } from '@/components/ui/shadcn/textarea';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/shadcn/tabs';
import { Checkbox } from '@/components/ui/shadcn/checkbox';
import { Select } from '@/components/ui/forms/Select/Select';
import { DateInput } from '@/components/ui/forms/DateInput/DateInput';
import { CurrencyInput } from '@/components/ui/forms/CurrencyInput/CurrencyInput';
import { CostCenterApportionment } from '@/components/ui/forms/CostCenterApportionment/CostCenterApportionment';
import { usePeople } from '@/hooks/usePeople';
import { useLocations } from '@/hooks/useLocations';
import { useContas } from '@/hooks/useContas';
import { useCategoriasLancamento } from '@/hooks/useCategoriasLancamento';
import { usePlanoContas } from '@/hooks/usePlanoContas';
import { useFornecedores } from '@/hooks/useFornecedores';
import { useCentrosCusto } from '@/hooks/useCentrosCusto';
import { useLancamentosFinanceiros } from '@/hooks/useLancamentosFinanceiros';
import { CostCenterAllocation } from '@/types/CostCenterAllocation';

// Schema de validação para despesa
const despesaSchema = z.object({
  // Aba Básico
  pessoaId: z.number().min(1, 'Cliente/Pessoa é obrigatório'),
  descricao: z.string().min(1, 'Descrição é obrigatória'),
  dataLancamento: z.string().optional(),
  dataCompetencia: z.string().optional(),
  contaId: z.number().min(1, 'Conta é obrigatória'),
  localId: z.number().optional(),
  
  // Aba Financeiro
  valor: z.number().min(0.01, 'Valor deve ser maior que zero'),
  categoriaLctoFinanceiroId: z.number().optional(),
  costCenterAllocations: z.array(z.object({
    id: z.string(),
    costCenterId: z.number(),
    costCenterName: z.string(),
    value: z.number(),
    percentage: z.number(),
  })).optional(),
  
  // Aba Adicional
  planoContaCredito: z.number().optional(),
  fornecedorId: z.number().optional(),
  observacao: z.string().optional(),
  
  // Repetição
  repetirDespesa: z.boolean().optional(),
  tipoRepeticao: z.enum(['este', 'esteEProximos']).optional(),
  periodicidade: z.enum(['mensal', 'semanal', 'anual']).optional(),
  quantidadeRepeticoes: z.number().min(1).max(12).optional(),
});

type DespesaFormData = z.infer<typeof despesaSchema>;

interface DespesaFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  despesa?: any;
}

export function DespesaFormDialog({ open, onOpenChange, onSuccess, despesa }: DespesaFormDialogProps) {
  const [activeTab, setActiveTab] = useState('basico');
  const [costCenterAllocations, setCostCenterAllocations] = useState<CostCenterAllocation[]>([]);

  // Hooks para carregar dados
  const { people } = usePeople();
  const { locations } = useLocations();
  const { contas } = useContas();
  const { categorias } = useCategoriasLancamento();
  const { planoContas } = usePlanoContas();
  const { fornecedores, fetchFornecedoresDropdown } = useFornecedores();
  const { centrosCusto } = useCentrosCusto();
  const { createLancamento } = useLancamentosFinanceiros();

  const form = useForm<DespesaFormData>({
    resolver: zodResolver(despesaSchema),
    defaultValues: {
      pessoaId: 0,
      descricao: '',
      dataLancamento: '',
      dataCompetencia: '',
      contaId: 0,
      localId: 0,
      valor: 0,
      categoriaLctoFinanceiroId: 0,
      planoContaCredito: 0,
      fornecedorId: 0,
      observacao: '',
      repetirDespesa: false,
      tipoRepeticao: 'este',
      periodicidade: 'mensal',
      quantidadeRepeticoes: 1,
      costCenterAllocations: [],
    },
  });

  // Carregar dados dos dropdowns
  useEffect(() => {
    if (open) {
      const loadData = async () => {
        try {
          await fetchFornecedoresDropdown();
        } catch (error) {
          console.error('Erro ao carregar fornecedores:', error);
        }
      };
      loadData();
    }
  }, [open, fetchFornecedoresDropdown]);

  // Função para lidar com mudanças nas alocações de centro de custo
  const handleCostCenterAllocationsChange = (allocations: CostCenterAllocation[]) => {
    setCostCenterAllocations(allocations);
    form.setValue('costCenterAllocations', allocations);
  };

  // Função de submit
  const onSubmit = async (data: DespesaFormData) => {
    try {
      // Remover costCenterAllocations do data para evitar conflito
      const { costCenterAllocations, ...dataWithoutCostCenter } = data;
      
      const despesaData = {
        ...dataWithoutCostCenter,
        // Converter alocações de centro de custo para o formato esperado pelo backend
        alocacoesCentroCusto: costCenterAllocations?.map(allocation => ({
          centroCustoId: allocation.costCenterId,
          valor: allocation.value,
          porcentagem: allocation.percentage,
        })),
        // Configurar repetição apenas se solicitada
        repetirDespesa: data.repetirDespesa,
        tipoRepeticao: data.repetirDespesa ? data.tipoRepeticao : undefined,
        periodicidade: data.repetirDespesa ? data.periodicidade : undefined,
        quantidadeRepeticoes: data.repetirDespesa && data.tipoRepeticao === 'esteEProximos'
          ? data.quantidadeRepeticoes
          : undefined,
      };

      console.log('Dados da despesa a serem enviados:', despesaData);
      
      await createLancamento(despesaData, 'despesa'); // Tipo 'despesa'
      onSuccess();
      onOpenChange(false);
      form.reset();
      setCostCenterAllocations([]);
    } catch (error) {
      console.error('Erro ao criar despesa:', error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{despesa ? 'Editar Despesa' : 'Nova Despesa'}</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="basico">Básico</TabsTrigger>
                <TabsTrigger value="financeiro">Financeiro</TabsTrigger>
                <TabsTrigger value="adicional">Adicional</TabsTrigger>
              </TabsList>

              {/* Aba Básico */}
              <TabsContent value="basico" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  {/* Cliente/Pessoa */}
                  <FormField
                    control={form.control}
                    name="pessoaId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Cliente/Pessoa *</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value?.toString() || ''}
                            onValueChange={(value) => field.onChange(parseInt(value, 10))}
                            placeholder={people.length > 0 ? "Selecione um cliente" : "Nenhum cliente encontrado"}
                            options={people.length > 0 ? people.map((person) => ({
                              value: person.id.toString(),
                              label: person.nome,
                            })) : [{ value: 'no-data', label: 'Nenhum cliente cadastrado', disabled: true }]}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Descrição */}
                  <FormField
                    control={form.control}
                    name="descricao"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Descrição *</FormLabel>
                        <FormControl>
                          <Input placeholder="Descrição da despesa" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {/* Data de Lançamento */}
                  <FormField
                    control={form.control}
                    name="dataLancamento"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Data de Lançamento</FormLabel>
                        <FormControl>
                          <DateInput
                            value={field.value}
                            onChange={field.onChange}
                            placeholder="Selecione a data"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Data de Competência */}
                  <FormField
                    control={form.control}
                    name="dataCompetencia"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Data de Competência</FormLabel>
                        <FormControl>
                          <DateInput
                            value={field.value}
                            onChange={field.onChange}
                            placeholder="Selecione a data"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {/* Conta */}
                  <FormField
                    control={form.control}
                    name="contaId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Conta *</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value?.toString() || ''}
                            onValueChange={(value) => field.onChange(parseInt(value, 10))}
                            placeholder={contas.length > 0 ? "Selecione uma conta" : "Nenhuma conta encontrada"}
                            options={contas.length > 0 ? contas.map((conta) => ({
                              value: conta.id.toString(),
                              label: conta.descricao,
                            })) : [{ value: 'no-data', label: 'Nenhuma conta cadastrada', disabled: true }]}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Local */}
                  <FormField
                    control={form.control}
                    name="localId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Local</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value?.toString() || ''}
                            onValueChange={(value) => field.onChange(parseInt(value, 10))}
                            placeholder={locations.length > 0 ? "Selecione um local" : "Nenhum local encontrado"}
                            options={locations.length > 0 ? locations.map((location) => ({
                              value: location.id.toString(),
                              label: location.nome,
                            })) : [{ value: 'no-data', label: 'Nenhum local cadastrado', disabled: true }]}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </TabsContent>

              {/* Aba Financeiro */}
              <TabsContent value="financeiro" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  {/* Valor */}
                  <FormField
                    control={form.control}
                    name="valor"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Valor *</FormLabel>
                        <FormControl>
                          <CurrencyInput
                            value={field.value}
                            onChange={field.onChange}
                            placeholder="R$ 0,00"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Categoria */}
                  <FormField
                    control={form.control}
                    name="categoriaLctoFinanceiroId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Categoria</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value?.toString() || ''}
                            onValueChange={(value) => field.onChange(parseInt(value, 10))}
                            placeholder={categorias.length > 0 ? "Selecione uma categoria" : "Nenhuma categoria encontrada"}
                            options={categorias.length > 0 ? categorias.map((categoria) => ({
                              value: categoria.id.toString(),
                              label: categoria.descricao,
                            })) : [{ value: 'no-data', label: 'Nenhuma categoria cadastrada', disabled: true }]}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Alocação por Centro de Custo */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Alocação por Centro de Custo</h3>
                  <CostCenterApportionment
                    costCenters={centrosCusto}
                    allocations={costCenterAllocations}
                    onAllocationsChange={handleCostCenterAllocationsChange}
                    totalAmount={form.watch('valor') || 0}
                  />
                </div>

                {/* Repetir Despesa */}
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="repetirDespesa"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Repetir despesa</FormLabel>
                        </div>
                      </FormItem>
                    )}
                  />

                  {form.watch('repetirDespesa') && (
                    <div className="grid grid-cols-3 gap-4 ml-6">
                      <FormField
                        control={form.control}
                        name="tipoRepeticao"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Tipo</FormLabel>
                            <FormControl>
                              <Select
                                value={field.value}
                                onValueChange={field.onChange}
                                placeholder="Selecione o tipo"
                                options={[
                                  { value: 'este', label: 'Apenas este mês' },
                                  { value: 'esteEProximos', label: 'Este e próximos meses' },
                                ]}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="periodicidade"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Periodicidade</FormLabel>
                            <FormControl>
                              <Select
                                value={field.value}
                                onValueChange={field.onChange}
                                placeholder="Selecione a periodicidade"
                                options={[
                                  { value: 'mensal', label: 'Mensal' },
                                  { value: 'semanal', label: 'Semanal' },
                                  { value: 'anual', label: 'Anual' },
                                ]}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {form.watch('tipoRepeticao') === 'esteEProximos' && (
                        <FormField
                          control={form.control}
                          name="quantidadeRepeticoes"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Quantidade</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="1"
                                  max="12"
                                  placeholder="1"
                                  {...field}
                                  onChange={(e) => field.onChange(parseInt(e.target.value, 10))}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}
                    </div>
                  )}
                </div>
              </TabsContent>

              {/* Aba Adicional */}
              <TabsContent value="adicional" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  {/* Plano de Conta de Crédito */}
                  <FormField
                    control={form.control}
                    name="planoContaCredito"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Plano de Conta de Crédito</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value?.toString() || ''}
                            onValueChange={(value) => field.onChange(parseInt(value, 10))}
                            placeholder={planoContas.length > 0 ? "Selecione um plano de conta" : "Nenhum plano de conta encontrado"}
                            options={planoContas.length > 0 ? planoContas.map((plano) => ({
                              value: plano.id.toString(),
                              label: plano.descricao,
                            })) : [{ value: 'no-data', label: 'Nenhum plano de conta cadastrado', disabled: true }]}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Fornecedor */}
                  <FormField
                    control={form.control}
                    name="fornecedorId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Fornecedor</FormLabel>
                        <FormControl>
                          <Select
                            value={field.value?.toString() || ''}
                            onValueChange={(value) => field.onChange(parseInt(value, 10))}
                            placeholder={fornecedores.length > 0 ? "Selecione um fornecedor" : "Nenhum fornecedor encontrado"}
                            options={fornecedores.length > 0 ? fornecedores.map((fornecedor) => ({
                              value: fornecedor.id.toString(),
                              label: fornecedor.descricao,
                            })) : [{ value: 'no-data', label: 'Nenhum fornecedor cadastrado', disabled: true }]}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Observação */}
                <FormField
                  control={form.control}
                  name="observacao"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Observação</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Observações adicionais sobre a despesa"
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>
            </Tabs>

            {/* Botões */}
            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancelar
              </Button>
              <Button type="submit">
                {despesa ? 'Atualizar' : 'Criar'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
