import { SelectQueryBuilder, ObjectLiteral } from 'typeorm';
export interface BaseEntity {
    id: number;
    dataHoraUsuarioInc: Date;
    dataHoraUsuarioAlt: Date;
}
export interface BasePaginationFilter {
    page?: number;
    limit?: number;
}
export interface PaginatedResponse<T> {
    data: T[];
    total: number;
    page: number;
    limit: number;
}
export declare abstract class BaseService {
    protected applyDefaultSorting<T extends BaseEntity>(queryBuilder: SelectQueryBuilder<T>, alias?: string): SelectQueryBuilder<T>;
    protected applyPagination<T extends ObjectLiteral>(queryBuilder: SelectQueryBuilder<T>, page?: number, limit?: number): SelectQueryBuilder<T>;
    protected createPaginatedResponse<T>(data: T[], total: number, page: number, limit: number): PaginatedResponse<T>;
    protected validatePaginationParams(page?: number, limit?: number): {
        page: number;
        limit: number;
    };
    protected applyStandardQuery<T extends BaseEntity & ObjectLiteral, F extends BasePaginationFilter>(queryBuilder: SelectQueryBuilder<T>, filterParams: F, alias?: string): Promise<{
        data: T[];
        total: number;
        page: number;
        limit: number;
    }>;
}
