"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const sgMail = __importStar(require("@sendgrid/mail"));
const nodemailer = __importStar(require("nodemailer"));
let EmailService = class EmailService {
    configService;
    transporter;
    constructor(configService) {
        this.configService = configService;
        const sendgridApiKey = this.configService.get('SENDGRID_API_KEY');
        if (sendgridApiKey) {
            sgMail.setApiKey(sendgridApiKey);
        }
        this.transporter = nodemailer.createTransport({
            host: this.configService.get('SMTP_HOST') || 'smtp.gmail.com',
            port: this.configService.get('SMTP_PORT') || 587,
            secure: false,
            auth: {
                user: this.configService.get('SMTP_USER'),
                pass: this.configService.get('SMTP_PASS'),
            },
        });
    }
    async sendVerificationCode(email, code) {
        const fromEmail = this.configService.get('EMAIL_FROM') || '<EMAIL>';
        const subject = 'AccountBee - Verificação em Duas Etapas';
        const html = `
      <div style="font-family: Arial, sans-serif; padding: 20px;">
        <h2>Verificação em Duas Etapas</h2>
        <p>Seu código para acesso ao AccountBee é:</p>
        <h1 style="color: #4CAF50; font-size: 32px; letter-spacing: 5px;">${code}</h1>
        <p>Este código é válido apenas para esta sessão de login.</p>
        <p style="color: #666; font-size: 12px;">Se você não solicitou este código, ignore este email.</p>
      </div>
    `;
        try {
            const sendgridApiKey = this.configService.get('SENDGRID_API_KEY');
            if (sendgridApiKey) {
                const msg = {
                    to: email,
                    from: fromEmail,
                    subject: subject,
                    html: html,
                };
                await sgMail.send(msg);
                console.log('Email enviado via SendGrid');
            }
            else {
                await this.transporter.sendMail({
                    from: fromEmail,
                    to: email,
                    subject: subject,
                    html: html,
                });
                console.log('Email enviado via SMTP');
            }
        }
        catch (error) {
            console.error('Erro ao enviar email:', error);
            console.log(`\n========================================`);
            console.log(`CÓDIGO DE VERIFICAÇÃO PARA ${email}: ${code}`);
            console.log(`========================================\n`);
        }
    }
    async sendPasswordResetEmail(email, resetLink) {
        const fromEmail = this.configService.get('EMAIL_FROM') || '<EMAIL>';
        const subject = 'AccountBee - Redefinição de Senha';
        const html = `
      <div style="font-family: Arial, sans-serif; padding: 20px;">
        <h2>Redefinição de Senha</h2>
        <p>Você solicitou a redefinição de sua senha no AccountBee.</p>
        <p>Clique no link abaixo para criar uma nova senha:</p>
        <a href="${resetLink}" style="display: inline-block; padding: 10px 20px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 5px;">Redefinir Senha</a>
        <p style="margin-top: 20px;">Ou copie e cole este link no seu navegador:</p>
        <p style="color: #666;">${resetLink}</p>
        <p style="color: #666; font-size: 12px; margin-top: 20px;">Se você não solicitou esta redefinição, ignore este email.</p>
      </div>
    `;
        try {
            const sendgridApiKey = this.configService.get('SENDGRID_API_KEY');
            if (sendgridApiKey) {
                const msg = {
                    to: email,
                    from: fromEmail,
                    subject: subject,
                    html: html,
                };
                await sgMail.send(msg);
            }
            else {
                await this.transporter.sendMail({
                    from: fromEmail,
                    to: email,
                    subject: subject,
                    html: html,
                });
            }
        }
        catch (error) {
            console.error('Erro ao enviar email de reset:', error);
            throw error;
        }
    }
};
exports.EmailService = EmailService;
exports.EmailService = EmailService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], EmailService);
//# sourceMappingURL=email.service.js.map