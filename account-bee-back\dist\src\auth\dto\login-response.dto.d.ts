export declare class UsuarioResponseDto {
    id: number;
    email?: string;
    nomeUsuario?: string;
    token?: string;
    empresaId?: number;
    admGeral?: boolean;
    admLocal?: boolean;
    operador?: boolean;
    operadorEstoque?: boolean;
    visualizaSaldosBancarios?: boolean;
    gerenciarLctosFinanceiros?: boolean;
    relatoriosFinanceiros?: boolean;
    gerenciarProdutos?: boolean;
    podeAcessarFiscal?: boolean;
    podeAcessarConfiguracoes?: boolean;
    twoAuthentication?: boolean;
}
export declare class LoginResponseDto {
    usuarioVo?: UsuarioResponseDto;
    codigo?: string;
    mensagem?: string;
    erro?: string;
    warning?: string;
    idObjetoSalvo?: number;
    tipo?: string;
}
