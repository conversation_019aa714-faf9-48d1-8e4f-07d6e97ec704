"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContaResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ContaResponseDto {
    id;
    banco;
    saldoInicial;
    dataSaldoInicial;
    contaAtiva;
    empresaId;
    dataHoraUsuarioInc;
    dataHoraUsuarioAlt;
    usuarioInc;
    usuarioAlt;
}
exports.ContaResponseDto = ContaResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da conta',
        example: 1,
    }),
    __metadata("design:type", Number)
], ContaResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome do banco',
        example: 'Banco do Brasil',
    }),
    __metadata("design:type", String)
], ContaResponseDto.prototype, "banco", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Saldo inicial da conta',
        example: 1000.5,
    }),
    __metadata("design:type", Number)
], ContaResponseDto.prototype, "saldoInicial", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data do saldo inicial',
        example: '2024-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], ContaResponseDto.prototype, "dataSaldoInicial", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Conta ativa',
        example: true,
    }),
    __metadata("design:type", Boolean)
], ContaResponseDto.prototype, "contaAtiva", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da empresa',
        example: 1,
    }),
    __metadata("design:type", Number)
], ContaResponseDto.prototype, "empresaId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de criação',
        example: '2024-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], ContaResponseDto.prototype, "dataHoraUsuarioInc", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de última alteração',
        example: '2024-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], ContaResponseDto.prototype, "dataHoraUsuarioAlt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Usuário que criou',
        example: '<EMAIL>',
    }),
    __metadata("design:type", String)
], ContaResponseDto.prototype, "usuarioInc", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Usuário que alterou',
        example: '<EMAIL>',
    }),
    __metadata("design:type", String)
], ContaResponseDto.prototype, "usuarioAlt", void 0);
//# sourceMappingURL=conta-response.dto.js.map