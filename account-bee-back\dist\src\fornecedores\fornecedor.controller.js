"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FornecedorController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const fornecedor_service_1 = require("./fornecedor.service");
const create_fornecedor_dto_1 = require("./dto/create-fornecedor.dto");
const update_fornecedor_dto_1 = require("./dto/update-fornecedor.dto");
const fornecedor_response_dto_1 = require("./dto/fornecedor-response.dto");
const dto_1 = require("../lancamentos-financeiros/dto");
const filter_fornecedor_dto_1 = require("./dto/filter-fornecedor.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let FornecedorController = class FornecedorController {
    fornecedorService;
    constructor(fornecedorService) {
        this.fornecedorService = fornecedorService;
    }
    async create(createFornecedorDto, req) {
        return this.fornecedorService.create(createFornecedorDto, req.user);
    }
    async findAll(req, filterDto) {
        return this.fornecedorService.findAll(req.user, filterDto);
    }
    async getDropdownOptions(req) {
        console.log('🔍 Controller dropdown - Usuário:', req?.user);
        const empresaId = req.user.empresaId;
        console.log('🔍 Controller dropdown - EmpresaId:', empresaId);
        return this.fornecedorService.findAllForDropdown(empresaId);
    }
    async findOne(id, req) {
        console.log('🔍 Controller findOne - ID recebido:', id, 'tipo:', typeof id);
        const numericId = parseInt(id, 10);
        if (isNaN(numericId)) {
            console.error('❌ Controller findOne - ID inválido:', id);
            throw new common_1.BadRequestException(`ID do fornecedor deve ser um número válido. Recebido: ${id}`);
        }
        return this.fornecedorService.findOne(numericId, req.user);
    }
    async update(id, updateFornecedorDto, req) {
        const numericId = parseInt(id, 10);
        if (isNaN(numericId)) {
            throw new common_1.BadRequestException('ID do fornecedor deve ser um número válido');
        }
        return this.fornecedorService.update(numericId, updateFornecedorDto, req.user);
    }
    async remove(id, req) {
        const numericId = parseInt(id, 10);
        if (isNaN(numericId)) {
            throw new common_1.BadRequestException('ID do fornecedor deve ser um número válido');
        }
        return this.fornecedorService.remove(numericId, req.user);
    }
};
exports.FornecedorController = FornecedorController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Criar novo fornecedor' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Fornecedor criado com sucesso',
        type: fornecedor_response_dto_1.FornecedorResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_fornecedor_dto_1.CreateFornecedorDto, Object]),
    __metadata("design:returntype", Promise)
], FornecedorController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Listar fornecedores com filtros' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista paginada de fornecedores',
        type: fornecedor_response_dto_1.PaginatedFornecedorResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    (0, swagger_1.ApiQuery)({
        name: 'status',
        required: false,
        type: String,
        description: 'Filtrar por status (ativo, excluido, todos)',
        enum: ['ativo', 'excluido', 'todos'],
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, filter_fornecedor_dto_1.FilterFornecedorDto]),
    __metadata("design:returntype", Promise)
], FornecedorController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('dropdown'),
    (0, swagger_1.ApiOperation)({ summary: 'Listar fornecedores para dropdown' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Lista de fornecedores para dropdown', type: [dto_1.FornecedorDropdownDto] }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], FornecedorController.prototype, "getDropdownOptions", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar fornecedor por ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID do fornecedor' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dados do fornecedor',
        type: fornecedor_response_dto_1.FornecedorResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Fornecedor não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], FornecedorController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar fornecedor' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID do fornecedor' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Fornecedor atualizado com sucesso',
        type: fornecedor_response_dto_1.FornecedorResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Fornecedor não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_fornecedor_dto_1.UpdateFornecedorDto, Object]),
    __metadata("design:returntype", Promise)
], FornecedorController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Excluir fornecedor (soft delete)' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID do fornecedor' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Fornecedor excluído com sucesso' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Fornecedor não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], FornecedorController.prototype, "remove", null);
exports.FornecedorController = FornecedorController = __decorate([
    (0, swagger_1.ApiTags)('fornecedores'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('fornecedores'),
    __metadata("design:paramtypes", [fornecedor_service_1.FornecedorService])
], FornecedorController);
//# sourceMappingURL=fornecedor.controller.js.map