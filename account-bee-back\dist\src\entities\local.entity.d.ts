import { Empresa } from './empresa.entity';
export declare class Local {
    id: number;
    idEmpresa: number;
    empresa: Empresa;
    descricao: string;
    endereco?: string;
    numero?: string;
    bairro?: string;
    cep?: string;
    telefone?: string;
    razaoSocial?: string;
    nomeFantasia?: string;
    idCidade?: number;
    complemento?: string;
    dataHoraUsuarioInc: Date;
    dataHoraUsuarioAlt: Date;
    dataHoraUsuarioDel?: Date;
    usuarioInc: string;
    usuarioAlt: string;
    usuarioDel?: string;
    isExcluido: string;
    uuid?: string;
    dataSync?: Date;
    utilizaTabelaPreco?: boolean;
    utilizaMobileCliente?: boolean;
    trabalhaPedido?: boolean;
    imprimePedido?: boolean;
    utilizaOffline?: boolean;
    cpfCnpj?: string;
    emailComercial?: string;
    latitude?: number;
    longitude?: number;
}
