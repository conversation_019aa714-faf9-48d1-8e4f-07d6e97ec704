"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContasController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const contas_service_1 = require("./contas.service");
const create_conta_dto_1 = require("./dto/create-conta.dto");
const update_conta_dto_1 = require("./dto/update-conta.dto");
const filter_contas_dto_1 = require("./dto/filter-contas.dto");
const conta_response_dto_1 = require("./dto/conta-response.dto");
const dto_1 = require("../lancamentos-financeiros/dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let ContasController = class ContasController {
    contasService;
    constructor(contasService) {
        this.contasService = contasService;
    }
    async create(createContaDto, req) {
        console.log('🔍 Controller - Dados recebidos:', createContaDto);
        console.log('🔍 Controller - Usuário:', req.user);
        const empresaId = req.user.empresaId;
        const usuarioEmail = req.user.email;
        console.log('🔍 Controller - EmpresaId:', empresaId);
        console.log('🔍 Controller - UsuarioEmail:', usuarioEmail);
        return this.contasService.create(createContaDto, empresaId, usuarioEmail);
    }
    async getDropdownOptions(req) {
        const empresaId = req.user.empresaId;
        return this.contasService.findAllForDropdown(empresaId);
    }
    async findAll(req, filterDto) {
        const empresaId = req.user.empresaId;
        return this.contasService.findAll(empresaId, filterDto);
    }
    async findOne(id, req) {
        const empresaId = req.user.empresaId;
        return this.contasService.findOne(+id, empresaId);
    }
    async update(id, updateContaDto, req) {
        const empresaId = req.user.empresaId;
        const usuarioEmail = req.user.email;
        return this.contasService.update(+id, updateContaDto, empresaId, usuarioEmail);
    }
    async remove(id, req) {
        const empresaId = req.user.empresaId;
        const usuarioEmail = req.user.email;
        return this.contasService.remove(+id, empresaId, usuarioEmail);
    }
};
exports.ContasController = ContasController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Criar nova conta bancária' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Conta criada com sucesso',
        type: conta_response_dto_1.ContaResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_conta_dto_1.CreateContaDto, Object]),
    __metadata("design:returntype", Promise)
], ContasController.prototype, "create", null);
__decorate([
    (0, common_1.Get)('dropdown'),
    (0, swagger_1.ApiOperation)({ summary: 'Listar contas para dropdown' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Lista de contas para dropdown', type: [dto_1.ContaDropdownDto] }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ContasController.prototype, "getDropdownOptions", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Listar contas bancárias' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de contas obtida com sucesso',
        type: [conta_response_dto_1.ContaResponseDto],
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    (0, swagger_1.ApiQuery)({
        name: 'contaAtiva',
        required: false,
        type: Boolean,
        description: 'Filtrar por contas ativas/inativas',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'banco',
        required: false,
        type: String,
        description: 'Filtrar por nome do banco',
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, filter_contas_dto_1.FilterContasDto]),
    __metadata("design:returntype", Promise)
], ContasController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Obter conta bancária por ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Conta obtida com sucesso',
        type: conta_response_dto_1.ContaResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Conta não encontrada' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ContasController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar conta bancária' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Conta atualizada com sucesso',
        type: conta_response_dto_1.ContaResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Conta não encontrada' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_conta_dto_1.UpdateContaDto, Object]),
    __metadata("design:returntype", Promise)
], ContasController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Excluir conta bancária (exclusão lógica)' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Conta excluída com sucesso' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Conta não encontrada' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ContasController.prototype, "remove", null);
exports.ContasController = ContasController = __decorate([
    (0, swagger_1.ApiTags)('contas'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('contas'),
    __metadata("design:paramtypes", [contas_service_1.ContasService])
], ContasController);
//# sourceMappingURL=contas.controller.js.map