{"version": 3, "file": "cnpj.validator.js", "sourceRoot": "", "sources": ["../../../../src/common/validators/cnpj.validator.ts"], "names": [], "mappings": ";;;;;;;;;AAkDA,wBAUC;AA5DD,qDAMyB;AAGlB,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAC3B,QAAQ,CAAC,IAAY,EAAE,IAAyB;QAC9C,IAAI,CAAC,IAAI;YAAE,OAAO,IAAI,CAAC;QAGvB,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAG1C,IAAI,SAAS,CAAC,MAAM,KAAK,EAAE;YAAE,OAAO,KAAK,CAAC;QAG1C,IAAI,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC;YAAE,OAAO,KAAK,CAAC;QAGjD,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,MAAM,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5B,GAAG,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC;QACD,IAAI,MAAM,GAAG,GAAG,GAAG,EAAE,CAAC;QACtB,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC;QACtC,IAAI,MAAM,KAAK,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAAE,OAAO,KAAK,CAAC;QAG5D,GAAG,GAAG,CAAC,CAAC;QACR,MAAM,QAAQ,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QACzD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5B,GAAG,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC;QACD,IAAI,MAAM,GAAG,GAAG,GAAG,EAAE,CAAC;QACtB,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC;QACtC,IAAI,MAAM,KAAK,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAAE,OAAO,KAAK,CAAC;QAE5D,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc,CAAC,IAAyB;QACtC,OAAO,eAAe,CAAC;IACzB,CAAC;CACF,CAAA;AAvCY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,qCAAmB,EAAC,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;GACzB,gBAAgB,CAuC5B;AAED,SAAgB,MAAM,CAAC,iBAAqC;IAC1D,OAAO,UAAU,MAAc,EAAE,YAAoB;QACnD,IAAA,mCAAiB,EAAC;YAChB,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,OAAO,EAAE,iBAAiB;YAC1B,WAAW,EAAE,EAAE;YACf,SAAS,EAAE,gBAAgB;SAC5B,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC"}