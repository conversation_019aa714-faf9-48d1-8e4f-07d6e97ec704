"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FilterPessoasDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class FilterPessoasDto {
    page = 1;
    limit = 10;
    search;
    empresaId;
    status = 'N';
    tipo;
}
exports.FilterPessoasDto = FilterPessoasDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Número da página',
        example: 1,
        default: 1,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: 'Página deve ser um número' }),
    (0, class_validator_1.Min)(1, { message: 'Página deve ser maior que 0' }),
    __metadata("design:type", Number)
], FilterPessoasDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Quantidade de itens por página',
        example: 10,
        default: 10,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: 'Limite deve ser um número' }),
    (0, class_validator_1.Min)(1, { message: 'Limite deve ser maior que 0' }),
    (0, class_validator_1.Max)(100, { message: 'Limite deve ser menor ou igual a 100' }),
    __metadata("design:type", Number)
], FilterPessoasDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Termo de busca (busca em nome, email, CPF, CNPJ)',
        example: 'João Silva',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Termo de busca deve ser uma string' }),
    __metadata("design:type", String)
], FilterPessoasDto.prototype, "search", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da empresa para filtrar',
        example: 1,
        required: true,
    }),
    (0, class_validator_1.IsNotEmpty)({
        message: 'empresaId must be a number conforming to the specified constraints',
    }),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, {
        message: 'empresaId must be a number conforming to the specified constraints',
    }),
    __metadata("design:type", Number)
], FilterPessoasDto.prototype, "empresaId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status do registro',
        example: 'N',
        enum: ['N', 'S', 'all'],
        default: 'N',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsIn)(['N', 'S', 'all'], { message: 'Status deve ser N, S ou all' }),
    __metadata("design:type", String)
], FilterPessoasDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tipo de pessoa (cpf ou cnpj)',
        example: 'cpf',
        enum: ['cpf', 'cnpj', 'all'],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsIn)(['cpf', 'cnpj', 'all'], { message: 'Tipo deve ser cpf, cnpj ou all' }),
    __metadata("design:type", String)
], FilterPessoasDto.prototype, "tipo", void 0);
//# sourceMappingURL=filter-pessoas.dto.js.map