"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmpresaResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
class EmpresaResponseDto {
    id;
    nomeEmpresa;
    razaoSocial;
    ddd;
    telefone;
    horaFimExpediente;
    dataInicioTeste;
    dataFimTeste;
    dataInicioFaturamento;
    dataCancelamento;
    sistemaLiberadoAte;
    statusEmpresa;
    diaParaPagamento;
    qtdUsuariosContratados;
    qtdUsuariosFree;
    valorPorUsuario;
    emailContabilidade;
}
exports.EmpresaResponseDto = EmpresaResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID único da empresa',
        example: 1,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], EmpresaResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome da empresa ou razão social',
        example: 'Empresa Exemplo LTDA',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], EmpresaResponseDto.prototype, "nomeEmpresa", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Razão social da empresa',
        example: 'Empresa Exemplo LTDA',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], EmpresaResponseDto.prototype, "razaoSocial", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'DDD da empresa',
        example: '11',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], EmpresaResponseDto.prototype, "ddd", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Telefone da empresa',
        example: '(11) 3333-4444',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], EmpresaResponseDto.prototype, "telefone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Hora de fim do expediente',
        example: '23:59',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], EmpresaResponseDto.prototype, "horaFimExpediente", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de início do teste',
        example: '2024-01-01',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], EmpresaResponseDto.prototype, "dataInicioTeste", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de fim do teste',
        example: '2024-12-31',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], EmpresaResponseDto.prototype, "dataFimTeste", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de início do faturamento',
        example: '2024-01-01',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], EmpresaResponseDto.prototype, "dataInicioFaturamento", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de cancelamento',
        example: '2024-12-31',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], EmpresaResponseDto.prototype, "dataCancelamento", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sistema liberado até',
        example: '2024-12-31',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], EmpresaResponseDto.prototype, "sistemaLiberadoAte", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status da empresa',
        example: 1,
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], EmpresaResponseDto.prototype, "statusEmpresa", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Dia para pagamento',
        example: 5,
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], EmpresaResponseDto.prototype, "diaParaPagamento", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Quantidade de usuários contratados',
        example: 10,
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], EmpresaResponseDto.prototype, "qtdUsuariosContratados", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Quantidade de usuários free',
        example: 5,
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], EmpresaResponseDto.prototype, "qtdUsuariosFree", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Valor por usuário',
        example: 29.9,
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], EmpresaResponseDto.prototype, "valorPorUsuario", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Email da contabilidade',
        example: '<EMAIL>',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], EmpresaResponseDto.prototype, "emailContabilidade", void 0);
//# sourceMappingURL=empresa-response.dto.js.map