"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseService = void 0;
class BaseService {
    applyDefaultSorting(queryBuilder, alias = 'entity') {
        return queryBuilder
            .orderBy(`${alias}.dataHoraUsuarioInc`, 'DESC')
            .addOrderBy(`${alias}.dataHoraUsuarioAlt`, 'DESC')
            .addOrderBy(`${alias}.id`, 'DESC');
    }
    applyPagination(queryBuilder, page = 1, limit = 10) {
        const skip = (page - 1) * limit;
        return queryBuilder.skip(skip).take(limit);
    }
    createPaginatedResponse(data, total, page, limit) {
        return {
            data,
            total,
            page,
            limit,
        };
    }
    validatePaginationParams(page, limit) {
        const validatedPage = Math.max(1, page || 1);
        const validatedLimit = Math.min(Math.max(1, limit || 10), 100);
        return {
            page: validatedPage,
            limit: validatedLimit,
        };
    }
    async applyStandardQuery(queryBuilder, filterParams, alias = 'entity') {
        const { page, limit } = this.validatePaginationParams(filterParams.page, filterParams.limit);
        this.applyDefaultSorting(queryBuilder, alias);
        this.applyPagination(queryBuilder, page, limit);
        const [data, total] = await queryBuilder.getManyAndCount();
        return this.createPaginatedResponse(data, total, page, limit);
    }
}
exports.BaseService = BaseService;
//# sourceMappingURL=base.service.js.map