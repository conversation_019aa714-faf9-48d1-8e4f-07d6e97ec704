"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const typeorm_1 = require("@nestjs/typeorm");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const auth_module_1 = require("./auth/auth.module");
const crypto_module_1 = require("./crypto/crypto.module");
const users_module_1 = require("./users/users.module");
const empresa_module_1 = require("./empresa/empresa.module");
const pessoas_module_1 = require("./pessoas/pessoas.module");
const locais_module_1 = require("./locais/locais.module");
const contas_module_1 = require("./contas/contas.module");
const centro_custo_module_1 = require("./centro-custo/centro-custo.module");
const fornecedor_routes_1 = require("./fornecedores/fornecedor.routes");
const lancamentos_financeiros_module_1 = require("./lancamentos-financeiros/lancamentos-financeiros.module");
const categorias_lancamento_module_1 = require("./categorias-lancamento/categorias-lancamento.module");
const plano_contas_module_1 = require("./plano-contas/plano-contas.module");
const usuario_entity_1 = require("./entities/usuario.entity");
const empresa_entity_1 = require("./entities/empresa.entity");
const local_entity_1 = require("./entities/local.entity");
const pessoa_entity_1 = require("./entities/pessoa.entity");
const conta_entity_1 = require("./entities/conta.entity");
const centro_custo_entity_1 = require("./entities/centro-custo.entity");
const fornecedor_entity_1 = require("./entities/fornecedor.entity");
const lancamento_financeiro_entity_1 = require("./entities/lancamento-financeiro.entity");
const financeiro_centro_custo_entity_1 = require("./entities/financeiro-centro-custo.entity");
const tipo_lancamento_financeiro_entity_1 = require("./entities/tipo-lancamento-financeiro.entity");
const categoria_lancamento_entity_1 = require("./entities/categoria-lancamento.entity");
const plano_conta_entity_1 = require("./entities/plano-conta.entity");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: '.env',
            }),
            typeorm_1.TypeOrmModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: (configService) => ({
                    type: 'mysql',
                    host: configService.get('DB_HOST'),
                    port: configService.get('DB_PORT'),
                    username: configService.get('DB_USERNAME'),
                    password: configService.get('DB_PASSWORD'),
                    database: configService.get('DB_DATABASE'),
                    entities: [
                        usuario_entity_1.Usuario,
                        empresa_entity_1.Empresa,
                        local_entity_1.Local,
                        pessoa_entity_1.Pessoa,
                        conta_entity_1.Conta,
                        centro_custo_entity_1.CentroCusto,
                        fornecedor_entity_1.Fornecedor,
                        lancamento_financeiro_entity_1.LancamentoFinanceiro,
                        financeiro_centro_custo_entity_1.FinanceiroCentroCusto,
                        tipo_lancamento_financeiro_entity_1.TipoLancamentoFinanceiro,
                        categoria_lancamento_entity_1.CategoriaLancamento,
                        plano_conta_entity_1.PlanoConta,
                    ],
                    synchronize: false,
                    logging: configService.get('NODE_ENV') === 'development',
                }),
                inject: [config_1.ConfigService],
            }),
            auth_module_1.AuthModule,
            crypto_module_1.CryptoModule,
            users_module_1.UsersModule,
            empresa_module_1.EmpresaModule,
            pessoas_module_1.PessoasModule,
            locais_module_1.LocaisModule,
            contas_module_1.ContasModule,
            centro_custo_module_1.CentroCustoModule,
            fornecedor_routes_1.FornecedorModule,
            lancamentos_financeiros_module_1.LancamentosFinanceirosModule,
            categorias_lancamento_module_1.CategoriasLancamentoModule,
            plano_contas_module_1.PlanoContasModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map