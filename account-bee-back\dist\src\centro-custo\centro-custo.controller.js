"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CentroCustoController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const centro_custo_service_1 = require("./centro-custo.service");
const create_centro_custo_dto_1 = require("./dto/create-centro-custo.dto");
const update_centro_custo_dto_1 = require("./dto/update-centro-custo.dto");
const filter_centro_custo_dto_1 = require("./dto/filter-centro-custo.dto");
const centro_custo_response_dto_1 = require("./dto/centro-custo-response.dto");
const dto_1 = require("../lancamentos-financeiros/dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let CentroCustoController = class CentroCustoController {
    centroCustoService;
    constructor(centroCustoService) {
        this.centroCustoService = centroCustoService;
    }
    async create(createCentroCustoDto, req) {
        console.log('🔍 Controller - Dados recebidos:', createCentroCustoDto);
        console.log('🔍 Controller - Usuário:', req.user);
        const empresaId = req.user.empresaId;
        const usuarioEmail = req.user.email;
        console.log('🔍 Controller - EmpresaId:', empresaId);
        console.log('🔍 Controller - UsuarioEmail:', usuarioEmail);
        return this.centroCustoService.create(createCentroCustoDto, empresaId, usuarioEmail);
    }
    async getDropdownOptions(req) {
        const empresaId = req.user.empresaId;
        return this.centroCustoService.findAllForDropdown(empresaId);
    }
    healthCheck() {
        console.log('🔍 Health check do centro-custo chamado');
        return { status: 'ok', module: 'centro-custo', timestamp: new Date().toISOString() };
    }
    async findAll(req, filterDto) {
        console.log('🔍 Controller findAll - Iniciando busca de centros de custo');
        console.log('🔍 Controller findAll - Usuário:', req?.user);
        console.log('🔍 Controller findAll - Filtros:', filterDto);
        const empresaId = req.user.empresaId;
        return this.centroCustoService.findAll(empresaId, filterDto);
    }
    async findOne(id, req) {
        const empresaId = req.user.empresaId;
        return this.centroCustoService.findOne(+id, empresaId);
    }
    async update(id, updateCentroCustoDto, req) {
        const empresaId = req.user.empresaId;
        const usuarioEmail = req.user.email;
        return this.centroCustoService.update(+id, updateCentroCustoDto, empresaId, usuarioEmail);
    }
    async remove(id, req) {
        const empresaId = req.user.empresaId;
        const usuarioEmail = req.user.email;
        return this.centroCustoService.remove(+id, empresaId, usuarioEmail);
    }
};
exports.CentroCustoController = CentroCustoController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Criar novo centro de custo' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Centro de custo criado com sucesso',
        type: centro_custo_response_dto_1.CentroCustoResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_centro_custo_dto_1.CreateCentroCustoDto, Object]),
    __metadata("design:returntype", Promise)
], CentroCustoController.prototype, "create", null);
__decorate([
    (0, common_1.Get)('dropdown'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Listar centros de custo para dropdown' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Lista de centros de custo para dropdown', type: [dto_1.CentroCustoDropdownDto] }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CentroCustoController.prototype, "getDropdownOptions", null);
__decorate([
    (0, common_1.Get)('health'),
    (0, swagger_1.ApiOperation)({ summary: 'Health check do módulo centro de custo' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Módulo funcionando' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], CentroCustoController.prototype, "healthCheck", null);
__decorate([
    (0, common_1.Get)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Listar centros de custo' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de centros de custo obtida com sucesso',
        type: [centro_custo_response_dto_1.CentroCustoResponseDto],
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    (0, swagger_1.ApiQuery)({
        name: 'status',
        required: false,
        type: String,
        description: 'Filtrar por status (ativo, excluido, todos)',
        enum: ['ativo', 'excluido', 'todos'],
    }),
    (0, swagger_1.ApiQuery)({
        name: 'descricao',
        required: false,
        type: String,
        description: 'Filtrar por descrição',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'eap',
        required: false,
        type: String,
        description: 'Filtrar por código EAP',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'uuid',
        required: false,
        type: String,
        description: 'Filtrar por UUID',
    }),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, filter_centro_custo_dto_1.FilterCentroCustoDto]),
    __metadata("design:returntype", Promise)
], CentroCustoController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Obter centro de custo por ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Centro de custo obtido com sucesso',
        type: centro_custo_response_dto_1.CentroCustoResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Centro de custo não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CentroCustoController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar centro de custo' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Centro de custo atualizado com sucesso',
        type: centro_custo_response_dto_1.CentroCustoResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Centro de custo não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_centro_custo_dto_1.UpdateCentroCustoDto, Object]),
    __metadata("design:returntype", Promise)
], CentroCustoController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Excluir centro de custo (exclusão lógica)' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Centro de custo excluído com sucesso',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Centro de custo não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Não autorizado' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CentroCustoController.prototype, "remove", null);
exports.CentroCustoController = CentroCustoController = __decorate([
    (0, swagger_1.ApiTags)('centro-custo'),
    (0, common_1.Controller)('centro-custo'),
    __metadata("design:paramtypes", [centro_custo_service_1.CentroCustoService])
], CentroCustoController);
//# sourceMappingURL=centro-custo.controller.js.map