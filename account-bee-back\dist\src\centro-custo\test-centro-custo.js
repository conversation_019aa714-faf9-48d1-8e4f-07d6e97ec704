"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.testCentroCusto = testCentroCusto;
const core_1 = require("@nestjs/core");
const app_module_1 = require("../app.module");
const centro_custo_service_1 = require("./centro-custo.service");
async function testCentroCusto() {
    console.log('🔍 Iniciando teste do módulo Centro de Custo...');
    try {
        const app = await core_1.NestFactory.create(app_module_1.AppModule);
        const centroCustoService = app.get(centro_custo_service_1.CentroCustoService);
        console.log('✅ Serviço obtido com sucesso');
        console.log('🔍 Testando listagem de centros de custo...');
        const centros = await centroCustoService.findAll(1, {});
        console.log('✅ Listagem executada. Quantidade encontrada:', centros.length);
        await app.close();
        console.log('✅ Teste concluído com sucesso!');
    }
    catch (error) {
        console.error('❌ Erro no teste:', error);
        if (error.message.includes('Table') && error.message.includes("doesn't exist")) {
            console.log('📋 A tabela centro_custo não existe no banco de dados.');
            console.log('📋 Execute o script SQL em: scripts/create-centro-custo-table.sql');
        }
    }
}
if (require.main === module) {
    testCentroCusto();
}
//# sourceMappingURL=test-centro-custo.js.map