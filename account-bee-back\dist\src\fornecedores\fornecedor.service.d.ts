import { FornecedorRepository } from './fornecedor.repository';
import { FornecedorValidation } from './fornecedor.validation';
import { CreateFornecedorDto } from './dto/create-fornecedor.dto';
import { UpdateFornecedorDto } from './dto/update-fornecedor.dto';
import { FilterFornecedorDto } from './dto/filter-fornecedor.dto';
import { FornecedorResponseDto, PaginatedFornecedorResponseDto } from './dto/fornecedor-response.dto';
import { FornecedorDropdownDto } from '../lancamentos-financeiros/dto';
export declare class FornecedorService {
    private readonly fornecedorRepository;
    private readonly fornecedorValidation;
    constructor(fornecedorRepository: FornecedorRepository, fornecedorValidation: FornecedorValidation);
    create(createFornecedorDto: CreateFornecedorDto, user: any): Promise<FornecedorResponseDto>;
    findAll(user: any, filterDto?: FilterFornecedorDto): Promise<PaginatedFornecedorResponseDto>;
    findOne(id: number, user: any): Promise<FornecedorResponseDto>;
    update(id: number, updateFornecedorDto: UpdateFornecedorDto, user: any): Promise<FornecedorResponseDto>;
    remove(id: number, user: any): Promise<void>;
    private mapToResponseDto;
    findAllForDropdown(empresaId: number): Promise<FornecedorDropdownDto[]>;
}
