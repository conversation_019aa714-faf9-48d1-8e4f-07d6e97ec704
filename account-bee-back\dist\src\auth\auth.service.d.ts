import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { Usuario } from '../entities/usuario.entity';
import { Empresa } from '../entities/empresa.entity';
import { LoginDto } from './dto/login.dto';
import { LoginResponseDto } from './dto/login-response.dto';
import { VerifyCodeDto } from './dto/verify-code.dto';
import { UserRegistrationDto } from './dto/user-registration.dto';
import { LegacyCryptoService } from '../crypto/legacy-crypto.service';
import { EmailService } from '../email/email.service';
import { EmpresaService } from '../empresa/empresa.service';
export declare class AuthService {
    private usuarioRepository;
    private empresaRepository;
    private jwtService;
    private cryptoService;
    private emailService;
    private empresaService;
    constructor(usuarioRepository: Repository<Usuario>, empresaRepository: Repository<Empresa>, jwtService: JwtService, cryptoService: LegacyCryptoService, emailService: EmailService, empresaService: EmpresaService);
    login(loginDto: LoginDto): Promise<LoginResponseDto>;
    verifyCode(verifyCodeDto: VerifyCodeDto): Promise<LoginResponseDto>;
    private authenticateUser;
    private generateToken;
    private generateVerificationCode;
    private saveVerificationCode;
    validateToken(token: string): Promise<Usuario | null>;
    register(registrationDto: UserRegistrationDto): Promise<LoginResponseDto>;
}
