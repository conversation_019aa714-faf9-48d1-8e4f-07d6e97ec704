"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Empresa = void 0;
const typeorm_1 = require("typeorm");
let Empresa = class Empresa {
    id;
    nomeEmpresa;
    razaoSocial;
    ddd;
    telefone;
    horaFimExpediente;
    dataInicioTeste;
    dataFimTeste;
    dataInicioFaturamento;
    dataCancelamento;
    sistemaLiberadoAte;
    sistemaLiberadoAteOld;
    franqueadora;
    idCloud;
    statusEmpresa;
    diaParaPagamento;
    permiteVendaAvulsa;
    exibirIndiqueGanhe;
    compartilhaClientesEntreLocais;
    ramoNegocio;
    qtdUsuariosContratados;
    utmCampaign;
    utmMedium;
    utmSource;
    contDesbloqueio;
    pedidosNaMao;
    qtdUsuariosFree;
    empresaTeste;
    leadEnviadoPara;
    valorPorUsuario;
    emailContabilidade;
    origemCadastro;
    dataHoraUsuarioAlt;
    dataHoraUsuarioDel;
    dataHoraUsuarioInc;
    dataSync;
    isExcluido;
    usuarioAlt;
    usuarioDel;
    usuarioInc;
    uuid;
};
exports.Empresa = Empresa;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'ID' }),
    __metadata("design:type", Number)
], Empresa.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'NOME_EMPRESA', length: 255, nullable: true }),
    __metadata("design:type", String)
], Empresa.prototype, "nomeEmpresa", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'RAZAO_SOCIAL', length: 255, nullable: true }),
    __metadata("design:type", String)
], Empresa.prototype, "razaoSocial", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DDD', length: 100, nullable: true }),
    __metadata("design:type", String)
], Empresa.prototype, "ddd", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'TELEFONE', length: 100, nullable: true }),
    __metadata("design:type", String)
], Empresa.prototype, "telefone", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'HORA_FIM_EXPEDIENTE', length: 5, nullable: true }),
    __metadata("design:type", String)
], Empresa.prototype, "horaFimExpediente", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_INICIO_TESTE', type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Empresa.prototype, "dataInicioTeste", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_FIM_TESTE', type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Empresa.prototype, "dataFimTeste", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_INICIO_FATURAMENTO', type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Empresa.prototype, "dataInicioFaturamento", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_CANCELAMENTO', type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Empresa.prototype, "dataCancelamento", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'SISTEMA_LIBERADO_ATE', type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Empresa.prototype, "sistemaLiberadoAte", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'SISTEMA_LIBERADO_ATE_OLD', type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Empresa.prototype, "sistemaLiberadoAteOld", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'FRANQUEADORA', type: 'boolean', nullable: true }),
    __metadata("design:type", Boolean)
], Empresa.prototype, "franqueadora", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ID_CLOUD', nullable: true }),
    __metadata("design:type", Number)
], Empresa.prototype, "idCloud", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'STATUS_EMPRESA', nullable: true }),
    __metadata("design:type", Number)
], Empresa.prototype, "statusEmpresa", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DIA_PARA_PAGAMENTO', nullable: true }),
    __metadata("design:type", Number)
], Empresa.prototype, "diaParaPagamento", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'PERMITE_VENDA_AVULSA', type: 'boolean', nullable: true }),
    __metadata("design:type", Boolean)
], Empresa.prototype, "permiteVendaAvulsa", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'EXIBIR_INDIQUE_GANHE', type: 'boolean', nullable: true }),
    __metadata("design:type", Boolean)
], Empresa.prototype, "exibirIndiqueGanhe", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'COMPARTILHA_CLIENTES_ENTRE_LOCAIS',
        type: 'boolean',
        nullable: true,
    }),
    __metadata("design:type", Boolean)
], Empresa.prototype, "compartilhaClientesEntreLocais", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'RAMO_NEGOCIO', nullable: true }),
    __metadata("design:type", Number)
], Empresa.prototype, "ramoNegocio", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'QTD_USUARIOS_CONTRATADOS', nullable: true }),
    __metadata("design:type", Number)
], Empresa.prototype, "qtdUsuariosContratados", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'UTM_CAMPAIGN', length: 255, nullable: true }),
    __metadata("design:type", String)
], Empresa.prototype, "utmCampaign", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'UTM_MEDIUM', length: 255, nullable: true }),
    __metadata("design:type", String)
], Empresa.prototype, "utmMedium", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'UTM_SOURCE', length: 255, nullable: true }),
    __metadata("design:type", String)
], Empresa.prototype, "utmSource", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CONT_DESBLOQUEIO', nullable: true }),
    __metadata("design:type", Number)
], Empresa.prototype, "contDesbloqueio", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'PEDIDOS_NA_MAO', type: 'boolean', nullable: true }),
    __metadata("design:type", Boolean)
], Empresa.prototype, "pedidosNaMao", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'QTD_USUARIOS_FREE', nullable: true }),
    __metadata("design:type", Number)
], Empresa.prototype, "qtdUsuariosFree", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'EMPRESA_TESTE', type: 'boolean', nullable: true }),
    __metadata("design:type", Boolean)
], Empresa.prototype, "empresaTeste", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'LEAD_ENVIADO_PARA', length: 255, nullable: true }),
    __metadata("design:type", String)
], Empresa.prototype, "leadEnviadoPara", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'VALOR_POR_USUARIO', type: 'double', nullable: true }),
    __metadata("design:type", Number)
], Empresa.prototype, "valorPorUsuario", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'EMAIL_CONTABILIDADE', length: 255, nullable: true }),
    __metadata("design:type", String)
], Empresa.prototype, "emailContabilidade", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ORIGEM_CADASTRO', length: 255, nullable: true }),
    __metadata("design:type", String)
], Empresa.prototype, "origemCadastro", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_HORA_USUARIO_ALT', type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], Empresa.prototype, "dataHoraUsuarioAlt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_HORA_USUARIO_DEL', type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], Empresa.prototype, "dataHoraUsuarioDel", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_HORA_USUARIO_INC', type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], Empresa.prototype, "dataHoraUsuarioInc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_SYNC', type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], Empresa.prototype, "dataSync", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_EXCLUIDO', length: 1, nullable: true, default: 'N' }),
    __metadata("design:type", String)
], Empresa.prototype, "isExcluido", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_ALT', length: 500, nullable: true }),
    __metadata("design:type", String)
], Empresa.prototype, "usuarioAlt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_DEL', length: 500, nullable: true }),
    __metadata("design:type", String)
], Empresa.prototype, "usuarioDel", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_INC', length: 500, nullable: true }),
    __metadata("design:type", String)
], Empresa.prototype, "usuarioInc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'UUID', length: 50, nullable: true }),
    __metadata("design:type", String)
], Empresa.prototype, "uuid", void 0);
exports.Empresa = Empresa = __decorate([
    (0, typeorm_1.Entity)({ name: 'empresa' })
], Empresa);
//# sourceMappingURL=empresa.entity.js.map