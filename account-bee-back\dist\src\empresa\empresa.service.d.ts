import { Repository } from 'typeorm';
import { Empresa } from '../entities/empresa.entity';
import { CreateEmpresaDto } from './dto/create-empresa.dto';
import { LegacyCryptoService } from '../crypto/legacy-crypto.service';
export declare class EmpresaService {
    private readonly empresaRepository;
    private readonly cryptoService;
    constructor(empresaRepository: Repository<Empresa>, cryptoService: LegacyCryptoService);
    create(createEmpresaDto: CreateEmpresaDto): Promise<Empresa>;
    findOne(id: number): Promise<Empresa>;
    findAll(): Promise<Empresa[]>;
    update(id: number, updateData: Partial<CreateEmpresaDto>): Promise<Empresa>;
    remove(id: number): Promise<void>;
}
