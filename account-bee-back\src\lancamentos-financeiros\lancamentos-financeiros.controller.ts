import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { LancamentosFinanceirosService } from './lancamentos-financeiros.service';
import {
  CreateLancamentoFinanceiroDto,
  LancamentoFinanceiroResponseDto,
  CostCenterAllocationDto
} from './dto';

@ApiTags('lancamentos-financeiros')
@Controller('lancamentos-financeiros')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class LancamentosFinanceirosController {
  constructor(
    private readonly lancamentosService: LancamentosFinanceirosService,
  ) {}

  @Post('receitas')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Criar nova receita (lançamento financeiro tipo 1)' })
  @ApiResponse({ 
    status: 201, 
    description: 'Receita criada com sucesso', 
    type: LancamentoFinanceiroResponseDto 
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async createReceita(
    @Body() createDto: CreateLancamentoFinanceiroDto,
    @Request() req: any,
  ): Promise<LancamentoFinanceiroResponseDto> {
    // Garantir que é uma receita
    createDto.tipoLancamentoFinanceiroId = 1;
    createDto.empresaId = req.user.empresaId;
    
    return this.lancamentosService.create(createDto, req.user);
  }

  @Post('despesas')
  @ApiOperation({ summary: 'Criar nova despesa (lançamento financeiro tipo 2)' })
  @ApiResponse({
    status: 201,
    description: 'Despesa criada com sucesso',
    type: LancamentoFinanceiroResponseDto
  })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async createDespesa(
    @Body() createDto: CreateLancamentoFinanceiroDto,
    @Request() req: any,
  ): Promise<LancamentoFinanceiroResponseDto> {
    // Garantir que é uma despesa
    createDto.tipoLancamentoFinanceiroId = 2; // 2 = Despesa
    createDto.empresaId = req.user.empresaId;

    return this.lancamentosService.create(createDto, req.user);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar lançamento financeiro por ID' })
  @ApiParam({ name: 'id', description: 'ID do lançamento financeiro' })
  @ApiResponse({ 
    status: 200, 
    description: 'Lançamento encontrado', 
    type: LancamentoFinanceiroResponseDto 
  })
  @ApiResponse({ status: 404, description: 'Lançamento não encontrado' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async findOne(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<LancamentoFinanceiroResponseDto> {
    const empresaId = req.user.empresaId;
    return this.lancamentosService.findById(+id, empresaId);
  }

  @Put(':id/alocacoes-centro-custo')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Atualizar alocações de centro de custo de um lançamento' })
  @ApiParam({ name: 'id', description: 'ID do lançamento financeiro' })
  @ApiResponse({ status: 204, description: 'Alocações atualizadas com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Lançamento não encontrado' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async updateCostCenterAllocations(
    @Param('id') id: string,
    @Body() allocations: CostCenterAllocationDto[],
    @Request() req: any,
  ): Promise<void> {
    const empresaId = req.user.empresaId;
    return this.lancamentosService.updateCostCenterAllocations(+id, allocations, empresaId, req.user);
  }

  @Get(':id/alocacoes-centro-custo')
  @ApiOperation({ summary: 'Listar alocações de centro de custo de um lançamento' })
  @ApiParam({ name: 'id', description: 'ID do lançamento financeiro' })
  @ApiResponse({ status: 200, description: 'Lista de alocações' })
  @ApiResponse({ status: 404, description: 'Lançamento não encontrado' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async getCostCenterAllocations(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<any[]> {
    const empresaId = req.user.empresaId;
    return this.lancamentosService.getCostCenterAllocations(+id, empresaId);
  }

  @Delete(':lancamentoId/alocacoes-centro-custo/:allocationId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Excluir uma alocação de centro de custo específica' })
  @ApiParam({ name: 'lancamentoId', description: 'ID do lançamento financeiro' })
  @ApiParam({ name: 'allocationId', description: 'ID da alocação de centro de custo' })
  @ApiResponse({ status: 204, description: 'Alocação excluída com sucesso' })
  @ApiResponse({ status: 404, description: 'Alocação não encontrada' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async deleteCostCenterAllocation(
    @Param('lancamentoId') lancamentoId: string,
    @Param('allocationId') allocationId: string,
    @Request() req: any,
  ): Promise<void> {
    const empresaId = req.user.empresaId;
    return this.lancamentosService.deleteCostCenterAllocation(+lancamentoId, +allocationId, empresaId, req.user);
  }

  @Get('test')
  @ApiOperation({ summary: 'Teste de conectividade' })
  @ApiResponse({ status: 200, description: 'Teste OK' })
  async test(@Request() req: any) {
    return {
      message: 'API funcionando',
      empresaId: req.user?.empresaId,
      timestamp: new Date().toISOString(),
    };
  }

  @Get()
  @ApiOperation({ summary: 'Listar lançamentos financeiros com filtros' })
  @ApiQuery({ name: 'tipo', required: false, description: 'Tipo de lançamento (1=Receita, 2=Despesa)' })
  @ApiQuery({ name: 'page', required: false, description: 'Página (padrão: 1)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Itens por página (padrão: 10)' })
  @ApiQuery({ name: 'dataInicio', required: false, description: 'Data de início (YYYY-MM-DD)' })
  @ApiQuery({ name: 'dataFim', required: false, description: 'Data de fim (YYYY-MM-DD)' })
  @ApiResponse({
    status: 200,
    description: 'Lista de lançamentos financeiros',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/LancamentoFinanceiroResponseDto' }
        },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
        totalPages: { type: 'number' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async findAll(
    @Request() req: any,
    @Query('tipo') tipo?: string,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10',
    @Query('dataInicio') dataInicio?: string,
    @Query('dataFim') dataFim?: string,
  ) {
    const pageNumber = parseInt(page, 10) || 1;
    const limitNumber = parseInt(limit, 10) || 10;
    const tipoNumber = tipo ? parseInt(tipo, 10) : undefined;

    return this.lancamentosService.findAll({
      empresaId: req.user.empresaId,
      tipo: tipoNumber,
      page: pageNumber,
      limit: limitNumber,
      dataInicio: dataInicio ? new Date(dataInicio) : undefined,
      dataFim: dataFim ? new Date(dataFim) : undefined,
    });
  }

  @Get('resumo')
  @ApiOperation({ summary: 'Obter resumo financeiro (totais por tipo)' })
  @ApiQuery({ name: 'dataInicio', required: false, description: 'Data de início (YYYY-MM-DD)' })
  @ApiQuery({ name: 'dataFim', required: false, description: 'Data de fim (YYYY-MM-DD)' })
  @ApiResponse({
    status: 200,
    description: 'Resumo financeiro',
    schema: {
      type: 'object',
      properties: {
        totalReceitas: { type: 'number' },
        totalDespesas: { type: 'number' },
        saldo: { type: 'number' },
        quantidadeReceitas: { type: 'number' },
        quantidadeDespesas: { type: 'number' }
      }
    }
  })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  async getResumo(
    @Request() req: any,
    @Query('dataInicio') dataInicio?: string,
    @Query('dataFim') dataFim?: string,
  ) {
    return this.lancamentosService.getResumoFinanceiro({
      empresaId: req.user.empresaId,
      dataInicio: dataInicio ? new Date(dataInicio) : undefined,
      dataFim: dataFim ? new Date(dataFim) : undefined,
    });
  }
}
