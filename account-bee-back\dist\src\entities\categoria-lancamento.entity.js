"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CategoriaLancamento = void 0;
const typeorm_1 = require("typeorm");
const empresa_entity_1 = require("./empresa.entity");
let CategoriaLancamento = class CategoriaLancamento {
    id;
    descricao;
    tipoCategoriaLancamento;
    diasParaCredito;
    taxa;
    empresaId;
    empresa;
    dataHoraUsuarioAlt;
    dataHoraUsuarioDel;
    dataHoraUsuarioInc;
    dataSync;
    isExcluido;
    usuarioAlt;
    usuarioDel;
    usuarioInc;
    uuid;
};
exports.CategoriaLancamento = CategoriaLancamento;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'ID' }),
    __metadata("design:type", Number)
], CategoriaLancamento.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DESCRICAO', length: 200, nullable: false }),
    __metadata("design:type", String)
], CategoriaLancamento.prototype, "descricao", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'TIPO_CATEGORIA_LANCAMENTO', length: 7, nullable: true }),
    __metadata("design:type", String)
], CategoriaLancamento.prototype, "tipoCategoriaLancamento", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DIAS_PARA_CREDITO', nullable: true }),
    __metadata("design:type", Number)
], CategoriaLancamento.prototype, "diasParaCredito", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'TAXA', type: 'decimal', precision: 10, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], CategoriaLancamento.prototype, "taxa", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'EMPRESA_ID', nullable: false }),
    __metadata("design:type", Number)
], CategoriaLancamento.prototype, "empresaId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => empresa_entity_1.Empresa),
    (0, typeorm_1.JoinColumn)({ name: 'EMPRESA_ID' }),
    __metadata("design:type", empresa_entity_1.Empresa)
], CategoriaLancamento.prototype, "empresa", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'DATA_HORA_USUARIO_ALT',
        type: 'datetime',
        precision: 6,
        nullable: false,
    }),
    __metadata("design:type", Date)
], CategoriaLancamento.prototype, "dataHoraUsuarioAlt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'DATA_HORA_USUARIO_DEL',
        type: 'datetime',
        precision: 6,
        nullable: true,
    }),
    __metadata("design:type", Date)
], CategoriaLancamento.prototype, "dataHoraUsuarioDel", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'DATA_HORA_USUARIO_INC',
        type: 'datetime',
        precision: 6,
        nullable: false,
    }),
    __metadata("design:type", Date)
], CategoriaLancamento.prototype, "dataHoraUsuarioInc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_SYNC', type: 'datetime', precision: 6, nullable: true }),
    __metadata("design:type", Date)
], CategoriaLancamento.prototype, "dataSync", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_EXCLUIDO', length: 1, nullable: false, default: 'N' }),
    __metadata("design:type", String)
], CategoriaLancamento.prototype, "isExcluido", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_ALT', length: 500, nullable: false }),
    __metadata("design:type", String)
], CategoriaLancamento.prototype, "usuarioAlt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_DEL', length: 500, nullable: true }),
    __metadata("design:type", String)
], CategoriaLancamento.prototype, "usuarioDel", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_INC', length: 500, nullable: false }),
    __metadata("design:type", String)
], CategoriaLancamento.prototype, "usuarioInc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'UUID', length: 50, nullable: true }),
    __metadata("design:type", String)
], CategoriaLancamento.prototype, "uuid", void 0);
exports.CategoriaLancamento = CategoriaLancamento = __decorate([
    (0, typeorm_1.Entity)({ name: 'CATEGORIA_LANCAMENTO' })
], CategoriaLancamento);
//# sourceMappingURL=categoria-lancamento.entity.js.map