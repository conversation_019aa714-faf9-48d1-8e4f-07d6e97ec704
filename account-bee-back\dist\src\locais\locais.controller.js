"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocaisController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const locais_service_1 = require("./locais.service");
const create_local_dto_1 = require("./dto/create-local.dto");
const update_local_dto_1 = require("./dto/update-local.dto");
const local_response_dto_1 = require("./dto/local-response.dto");
const filter_locais_dto_1 = require("./dto/filter-locais.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../common/guards/roles.guard");
const permissions_guard_1 = require("../common/guards/permissions.guard");
const roles_decorator_1 = require("../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../common/decorators/permissions.decorator");
const user_roles_enum_1 = require("../common/enums/user-roles.enum");
let LocaisController = class LocaisController {
    locaisService;
    constructor(locaisService) {
        this.locaisService = locaisService;
    }
    create(createLocalDto, req) {
        return this.locaisService.create(createLocalDto, req.user);
    }
    findAll(filterDto, req) {
        return this.locaisService.findAll(filterDto, req.user);
    }
    findByEmpresa(idEmpresa, req) {
        return this.locaisService.findByEmpresa(+idEmpresa, req.user);
    }
    findOne(id, req) {
        return this.locaisService.findOne(+id, req.user);
    }
    update(id, updateLocalDto, req) {
        return this.locaisService.update(+id, updateLocalDto, req.user);
    }
    toggleStatus(id, req) {
        return this.locaisService.toggleStatus(+id, req.user);
    }
    remove(id, req) {
        return this.locaisService.remove(+id, req.user);
    }
};
exports.LocaisController = LocaisController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(user_roles_enum_1.UserRole.ADMIN_GERAL, user_roles_enum_1.UserRole.ADMIN_LOCAL, user_roles_enum_1.UserRole.OPERADOR),
    (0, permissions_decorator_1.RequirePermissions)(user_roles_enum_1.UserPermission.USERS_CREATE),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar novo local',
        description: 'Cria um novo local vinculado automaticamente à empresa do usuário logado. Dados sensíveis são criptografados.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Local criado com sucesso',
        type: local_response_dto_1.LocalResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Dados inválidos ou erro de validação',
        schema: {
            type: 'object',
            properties: {
                statusCode: { type: 'number', example: 400 },
                message: { type: 'string', example: 'Nome do local é obrigatório' },
                error: { type: 'string', example: 'Bad Request' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Nome, email comercial ou CPF/CNPJ já cadastrado para a empresa',
        schema: {
            type: 'object',
            properties: {
                statusCode: { type: 'number', example: 409 },
                message: {
                    type: 'string',
                    example: 'Nome do local já cadastrado para esta empresa',
                },
                error: { type: 'string', example: 'Conflict' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Sem permissão para criar locais',
        schema: {
            type: 'object',
            properties: {
                statusCode: { type: 'number', example: 403 },
                message: {
                    type: 'string',
                    example: 'Você só pode criar locais para sua própria empresa',
                },
                error: { type: 'string', example: 'Forbidden' },
            },
        },
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_local_dto_1.CreateLocalDto, Object]),
    __metadata("design:returntype", void 0)
], LocaisController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, permissions_decorator_1.RequirePermissions)(user_roles_enum_1.UserPermission.USERS_READ),
    (0, swagger_1.ApiOperation)({
        summary: 'Listar locais com filtros e paginação',
        description: 'Lista todos os locais com opções de filtro, busca e paginação. Usuários não-administradores só veem locais de sua empresa.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de locais com paginação',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/LocalResponseDto' },
                },
                total: {
                    type: 'number',
                    description: 'Total de registros encontrados',
                },
                page: { type: 'number', description: 'Página atual' },
                limit: { type: 'number', description: 'Itens por página' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Token de autenticação inválido ou expirado',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Sem permissão para visualizar locais',
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [filter_locais_dto_1.FilterLocaisDto, Object]),
    __metadata("design:returntype", void 0)
], LocaisController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('empresa/:idEmpresa'),
    (0, permissions_decorator_1.RequirePermissions)(user_roles_enum_1.UserPermission.USERS_READ),
    (0, swagger_1.ApiOperation)({
        summary: 'Listar locais por empresa',
        description: 'Lista todos os locais ativos de uma empresa específica. Apenas administradores gerais podem visualizar locais de outras empresas.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'idEmpresa',
        description: 'ID da empresa para buscar locais',
        type: 'number',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de locais da empresa',
        type: [local_response_dto_1.LocalResponseDto],
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Sem permissão para visualizar locais desta empresa',
        schema: {
            type: 'object',
            properties: {
                statusCode: { type: 'number', example: 403 },
                message: {
                    type: 'string',
                    example: 'Sem permissão para visualizar locais desta empresa',
                },
                error: { type: 'string', example: 'Forbidden' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Empresa não encontrada',
    }),
    __param(0, (0, common_1.Param)('idEmpresa')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], LocaisController.prototype, "findByEmpresa", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, permissions_decorator_1.RequirePermissions)(user_roles_enum_1.UserPermission.USERS_READ),
    (0, swagger_1.ApiOperation)({
        summary: 'Buscar local por ID',
        description: 'Busca um local específico pelo ID. Dados sensíveis são descriptografados automaticamente.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID único do local',
        type: 'number',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dados do local encontrado',
        type: local_response_dto_1.LocalResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Local não encontrado',
        schema: {
            type: 'object',
            properties: {
                statusCode: { type: 'number', example: 404 },
                message: { type: 'string', example: 'Local não encontrado' },
                error: { type: 'string', example: 'Not Found' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Sem permissão para visualizar este local',
        schema: {
            type: 'object',
            properties: {
                statusCode: { type: 'number', example: 403 },
                message: {
                    type: 'string',
                    example: 'Sem permissão para visualizar este local',
                },
                error: { type: 'string', example: 'Forbidden' },
            },
        },
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], LocaisController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)(user_roles_enum_1.UserRole.ADMIN_GERAL, user_roles_enum_1.UserRole.ADMIN_LOCAL, user_roles_enum_1.UserRole.OPERADOR),
    (0, permissions_decorator_1.RequirePermissions)(user_roles_enum_1.UserPermission.USERS_UPDATE),
    (0, swagger_1.ApiOperation)({
        summary: 'Atualizar local',
        description: 'Atualiza os dados de um local existente. A empresa não pode ser alterada após a criação.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID único do local a ser atualizado',
        type: 'number',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Local atualizado com sucesso',
        type: local_response_dto_1.LocalResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Local não encontrado',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Sem permissão para editar este local',
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Nome, email comercial ou CPF/CNPJ já cadastrado para a empresa',
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Dados inválidos ou erro de validação',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_local_dto_1.UpdateLocalDto, Object]),
    __metadata("design:returntype", void 0)
], LocaisController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    (0, roles_decorator_1.Roles)(user_roles_enum_1.UserRole.ADMIN_GERAL, user_roles_enum_1.UserRole.ADMIN_LOCAL),
    (0, permissions_decorator_1.RequirePermissions)(user_roles_enum_1.UserPermission.USERS_UPDATE),
    (0, swagger_1.ApiOperation)({
        summary: 'Ativar/desativar local',
        description: 'Alterna o status de ativo/inativo de um local. Apenas administradores podem executar esta ação.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID único do local',
        type: 'number',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Status do local alterado com sucesso',
        type: local_response_dto_1.LocalResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Local não encontrado',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Sem permissão para alterar status deste local',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], LocaisController.prototype, "toggleStatus", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, roles_decorator_1.Roles)(user_roles_enum_1.UserRole.ADMIN_GERAL, user_roles_enum_1.UserRole.ADMIN_LOCAL),
    (0, permissions_decorator_1.RequirePermissions)(user_roles_enum_1.UserPermission.USERS_DELETE),
    (0, swagger_1.ApiOperation)({
        summary: 'Excluir local (soft delete)',
        description: 'Executa a exclusão lógica (soft delete) de um local. O registro não é removido fisicamente do banco de dados.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID único do local a ser excluído',
        type: 'number',
        example: 1,
    }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Local excluído com sucesso (sem conteúdo retornado)',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Local não encontrado',
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Sem permissão para excluir este local',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], LocaisController.prototype, "remove", null);
exports.LocaisController = LocaisController = __decorate([
    (0, swagger_1.ApiTags)('locais'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard, permissions_guard_1.PermissionsGuard),
    (0, common_1.Controller)('locais'),
    __metadata("design:paramtypes", [locais_service_1.LocaisService])
], LocaisController);
//# sourceMappingURL=locais.controller.js.map