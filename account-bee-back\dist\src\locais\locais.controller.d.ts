import { LocaisService } from './locais.service';
import { CreateLocalDto } from './dto/create-local.dto';
import { UpdateLocalDto } from './dto/update-local.dto';
import { LocalResponseDto } from './dto/local-response.dto';
import { FilterLocaisDto } from './dto/filter-locais.dto';
export declare class LocaisController {
    private readonly locaisService;
    constructor(locaisService: LocaisService);
    create(createLocalDto: CreateLocalDto, req: any): Promise<LocalResponseDto>;
    findAll(filterDto: FilterLocaisDto, req: any): Promise<{
        data: LocalResponseDto[];
        total: number;
        page: number;
        limit: number;
    }>;
    findByEmpresa(idEmpresa: string, req: any): Promise<LocalResponseDto[]>;
    findOne(id: string, req: any): Promise<LocalResponseDto>;
    update(id: string, updateLocalDto: UpdateLocalDto, req: any): Promise<LocalResponseDto>;
    toggleStatus(id: string, req: any): Promise<LocalResponseDto>;
    remove(id: string, req: any): Promise<void>;
}
