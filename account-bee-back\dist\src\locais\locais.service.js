"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocaisService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const local_entity_1 = require("../entities/local.entity");
const empresa_entity_1 = require("../entities/empresa.entity");
const local_response_dto_1 = require("./dto/local-response.dto");
const legacy_crypto_service_1 = require("../crypto/legacy-crypto.service");
const class_transformer_1 = require("class-transformer");
const base_service_1 = require("../common/services/base.service");
let LocaisService = class LocaisService extends base_service_1.BaseService {
    localRepository;
    empresaRepository;
    cryptoService;
    constructor(localRepository, empresaRepository, cryptoService) {
        super();
        this.localRepository = localRepository;
        this.empresaRepository = empresaRepository;
        this.cryptoService = cryptoService;
    }
    async create(createLocalDto, currentUser) {
        let idEmpresa = createLocalDto.idEmpresa;
        if (!currentUser.admGeral || !idEmpresa) {
            idEmpresa = currentUser.empresaId;
        }
        const empresa = await this.empresaRepository.findOne({
            where: { id: idEmpresa },
            select: [
                'id',
                'nomeEmpresa',
                'razaoSocial',
                'telefone',
                'ddd',
                'horaFimExpediente',
                'statusEmpresa',
                'isExcluido',
            ],
        });
        if (!empresa) {
            throw new common_1.BadRequestException('Empresa não encontrada');
        }
        if (!currentUser.admGeral &&
            createLocalDto.idEmpresa &&
            createLocalDto.idEmpresa !== currentUser.empresaId) {
            throw new common_1.BadRequestException('Você só pode criar locais para sua própria empresa');
        }
        await this.checkDuplicates(createLocalDto, idEmpresa);
        const normalizedDto = this.normalizeSensitiveData(createLocalDto);
        const encryptedData = await this.encryptSensitiveData(normalizedDto, idEmpresa);
        const now = new Date();
        const local = this.localRepository.create({
            ...createLocalDto,
            ...encryptedData,
            descricao: createLocalDto.nome,
            idEmpresa: idEmpresa,
            isExcluido: 'N',
            dataHoraUsuarioInc: now,
            dataHoraUsuarioAlt: now,
            usuarioInc: currentUser.email || 'sistema',
            usuarioAlt: currentUser.email || 'sistema',
        });
        const savedLocal = await this.localRepository.save(local);
        return this.prepareLocalResponse(savedLocal);
    }
    async findAll(filterDto, currentUser) {
        const { page = 1, limit = 10, search, idEmpresa, status = 'N', idCidade, cep, bairro, } = filterDto;
        const where = {};
        if (status !== 'all') {
            where.isExcluido = status;
        }
        if (!currentUser.admGeral) {
            where.idEmpresa = currentUser.empresaId;
        }
        else if (idEmpresa) {
            where.idEmpresa = idEmpresa;
        }
        const queryBuilder = this.localRepository.createQueryBuilder('local');
        queryBuilder.where(where);
        if (search) {
            queryBuilder.andWhere('(local.descricao LIKE :search OR local.endereco LIKE :search OR local.bairro LIKE :search OR local.nomeFantasia LIKE :search)', { search: `%${search}%` });
        }
        if (idCidade) {
            queryBuilder.andWhere('local.idCidade = :idCidade', { idCidade });
        }
        if (cep) {
            queryBuilder.andWhere('local.cep LIKE :cep', { cep: `%${cep}%` });
        }
        if (bairro) {
            queryBuilder.andWhere('local.bairro LIKE :bairro', {
                bairro: `%${bairro}%`,
            });
        }
        const { page: validatedPage, limit: validatedLimit } = this.validatePaginationParams(page, limit);
        this.applyDefaultSorting(queryBuilder, 'local');
        this.applyPagination(queryBuilder, validatedPage, validatedLimit);
        const [locais, total] = await queryBuilder.getManyAndCount();
        return this.createPaginatedResponse(locais.map((local) => this.prepareLocalResponse(local)), total, validatedPage, validatedLimit);
    }
    async findOne(id, currentUser) {
        const local = await this.localRepository.findOne({
            where: { id, isExcluido: 'N' },
        });
        if (!local) {
            throw new common_1.NotFoundException('Local não encontrado');
        }
        if (!currentUser.admGeral && local.idEmpresa !== currentUser.empresaId) {
            throw new common_1.ForbiddenException('Sem permissão para visualizar este local');
        }
        return this.prepareLocalResponse(local);
    }
    async update(id, updateLocalDto, currentUser) {
        const local = await this.localRepository.findOne({
            where: { id, isExcluido: 'N' },
        });
        if (!local) {
            throw new common_1.NotFoundException('Local não encontrado');
        }
        if (!currentUser.admGeral && local.idEmpresa !== currentUser.empresaId) {
            throw new common_1.ForbiddenException('Sem permissão para editar este local');
        }
        await this.checkDuplicates(updateLocalDto, local.idEmpresa, id);
        const normalizedDto = this.normalizeSensitiveData(updateLocalDto);
        const encryptedData = await this.encryptSensitiveData(normalizedDto, local.idEmpresa);
        Object.assign(local, updateLocalDto, encryptedData);
        if (updateLocalDto.nome) {
            local.descricao = updateLocalDto.nome;
        }
        local.dataHoraUsuarioAlt = new Date();
        local.usuarioAlt = currentUser.email || 'sistema';
        const updatedLocal = await this.localRepository.save(local);
        return this.prepareLocalResponse(updatedLocal);
    }
    async remove(id, currentUser) {
        const local = await this.localRepository.findOne({
            where: { id, isExcluido: 'N' },
        });
        if (!local) {
            throw new common_1.NotFoundException('Local não encontrado');
        }
        if (!currentUser.admGeral && local.idEmpresa !== currentUser.empresaId) {
            throw new common_1.ForbiddenException('Sem permissão para excluir este local');
        }
        local.isExcluido = 'S';
        local.dataHoraUsuarioDel = new Date();
        local.usuarioDel = currentUser.email || 'sistema';
        await this.localRepository.save(local);
    }
    async toggleStatus(id, currentUser) {
        const local = await this.localRepository.findOne({
            where: { id },
        });
        if (!local) {
            throw new common_1.NotFoundException('Local não encontrado');
        }
        if (!currentUser.admGeral && local.idEmpresa !== currentUser.empresaId) {
            throw new common_1.ForbiddenException('Sem permissão para alterar status deste local');
        }
        local.isExcluido = local.isExcluido === 'N' ? 'S' : 'N';
        local.dataHoraUsuarioAlt = new Date();
        local.usuarioAlt = currentUser.email || 'sistema';
        const updatedLocal = await this.localRepository.save(local);
        return this.prepareLocalResponse(updatedLocal);
    }
    async findByEmpresa(idEmpresa, currentUser) {
        if (!currentUser.admGeral && idEmpresa !== currentUser.empresaId) {
            throw new common_1.ForbiddenException('Sem permissão para visualizar locais desta empresa');
        }
        const queryBuilder = this.localRepository.createQueryBuilder('local');
        queryBuilder.where({ idEmpresa, isExcluido: 'N' });
        this.applyDefaultSorting(queryBuilder, 'local');
        const locais = await queryBuilder.getMany();
        return locais.map((local) => this.prepareLocalResponse(local));
    }
    async checkDuplicates(localDto, idEmpresa, excludeId) {
        const where = {
            idEmpresa,
            isExcluido: 'N',
        };
        if (excludeId) {
            where.id = (0, typeorm_2.Not)(excludeId);
        }
        const nome = localDto.nome || localDto.descricao;
        if (nome) {
            const existingByNome = await this.localRepository.findOne({
                where: { ...where, descricao: nome },
            });
            if (existingByNome) {
                throw new common_1.ConflictException('Nome do local já cadastrado para esta empresa');
            }
        }
    }
    async encryptSensitiveData(localDto, idEmpresa) {
        const encryptedData = {};
        return encryptedData;
    }
    prepareLocalResponse(local) {
        const decryptedLocal = { ...local };
        const response = (0, class_transformer_1.plainToClass)(local_response_dto_1.LocalResponseDto, decryptedLocal, {
            excludeExtraneousValues: true,
        });
        return response;
    }
    decryptField(encryptedValue, idEmpresa) {
        if (encryptedValue &&
            !encryptedValue.includes('=') &&
            !encryptedValue.includes('+')) {
            return encryptedValue;
        }
        const keysToTry = [idEmpresa, 0, 1, 2, 3, 4, 5, 10, 20, 50, 100, 999];
        for (const key of keysToTry) {
            const decrypted = this.cryptoService.decrypt(encryptedValue, key);
            if (decrypted && decrypted.length > 0) {
                return decrypted;
            }
        }
        return null;
    }
    normalizeSensitiveData(localDto) {
        const normalized = { ...localDto };
        if (normalized.cep) {
            normalized.cep = normalized.cep.replace(/\D/g, '');
        }
        return normalized;
    }
};
exports.LocaisService = LocaisService;
exports.LocaisService = LocaisService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(local_entity_1.Local)),
    __param(1, (0, typeorm_1.InjectRepository)(empresa_entity_1.Empresa)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        legacy_crypto_service_1.LegacyCryptoService])
], LocaisService);
//# sourceMappingURL=locais.service.js.map