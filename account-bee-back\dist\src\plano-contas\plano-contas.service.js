"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlanoContasService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const plano_conta_entity_1 = require("../entities/plano-conta.entity");
let PlanoContasService = class PlanoContasService {
    planoContaRepository;
    constructor(planoContaRepository) {
        this.planoContaRepository = planoContaRepository;
    }
    async findAllByEmpresa(empresaId) {
        console.log('🔍 PlanoContasService - Buscando plano de contas para empresa:', empresaId);
        const planoContas = await this.planoContaRepository.find({
            where: {
                empresaId,
                isExcluido: 'N',
            },
            order: {
                descricao: 'ASC',
            },
        });
        console.log('✅ PlanoContasService - Encontrados', planoContas.length, 'planos de conta');
        return planoContas.map(planoConta => ({
            id: planoConta.id,
            descricao: planoConta.descricao,
            eap: planoConta.eap,
        }));
    }
    async findById(id, empresaId) {
        return this.planoContaRepository.findOne({
            where: {
                id,
                empresaId,
                isExcluido: 'N',
            },
        });
    }
};
exports.PlanoContasService = PlanoContasService;
exports.PlanoContasService = PlanoContasService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(plano_conta_entity_1.PlanoConta)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], PlanoContasService);
//# sourceMappingURL=plano-contas.service.js.map