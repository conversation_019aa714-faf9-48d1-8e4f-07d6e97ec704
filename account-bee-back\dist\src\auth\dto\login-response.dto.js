"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoginResponseDto = exports.UsuarioResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class UsuarioResponseDto {
    id;
    email;
    nomeUsuario;
    token;
    empresaId;
    admGeral;
    admLocal;
    operador;
    operadorEstoque;
    visualizaSaldosBancarios;
    gerenciarLctosFinanceiros;
    relatoriosFinanceiros;
    gerenciarProdutos;
    podeAcessarFiscal;
    podeAcessarConfiguracoes;
    twoAuthentication;
}
exports.UsuarioResponseDto = UsuarioResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)(),
    __metadata("design:type", Number)
], UsuarioResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], UsuarioResponseDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], UsuarioResponseDto.prototype, "nomeUsuario", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", String)
], UsuarioResponseDto.prototype, "token", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Number)
], UsuarioResponseDto.prototype, "empresaId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Boolean)
], UsuarioResponseDto.prototype, "admGeral", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Boolean)
], UsuarioResponseDto.prototype, "admLocal", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Boolean)
], UsuarioResponseDto.prototype, "operador", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Boolean)
], UsuarioResponseDto.prototype, "operadorEstoque", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Boolean)
], UsuarioResponseDto.prototype, "visualizaSaldosBancarios", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Boolean)
], UsuarioResponseDto.prototype, "gerenciarLctosFinanceiros", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Boolean)
], UsuarioResponseDto.prototype, "relatoriosFinanceiros", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Boolean)
], UsuarioResponseDto.prototype, "gerenciarProdutos", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Boolean)
], UsuarioResponseDto.prototype, "podeAcessarFiscal", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Boolean)
], UsuarioResponseDto.prototype, "podeAcessarConfiguracoes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ required: false }),
    __metadata("design:type", Boolean)
], UsuarioResponseDto.prototype, "twoAuthentication", void 0);
class LoginResponseDto {
    usuarioVo;
    codigo;
    mensagem;
    erro;
    warning;
    idObjetoSalvo;
    tipo;
}
exports.LoginResponseDto = LoginResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Usuário autenticado',
        required: false,
    }),
    __metadata("design:type", UsuarioResponseDto)
], LoginResponseDto.prototype, "usuarioVo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Código de status',
        required: false,
    }),
    __metadata("design:type", String)
], LoginResponseDto.prototype, "codigo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mensagem de resposta',
    }),
    __metadata("design:type", String)
], LoginResponseDto.prototype, "mensagem", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mensagem de erro',
        required: false,
    }),
    __metadata("design:type", String)
], LoginResponseDto.prototype, "erro", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Mensagem de aviso',
        required: false,
    }),
    __metadata("design:type", String)
], LoginResponseDto.prototype, "warning", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID do objeto salvo',
        required: false,
    }),
    __metadata("design:type", Number)
], LoginResponseDto.prototype, "idObjetoSalvo", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tipo de mensagem (success, danger, warning, info)',
        required: false,
    }),
    __metadata("design:type", String)
], LoginResponseDto.prototype, "tipo", void 0);
//# sourceMappingURL=login-response.dto.js.map