"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CentroCustoDropdownDto = exports.ContaDropdownDto = exports.FornecedorDropdownDto = exports.PlanoContaDropdownDto = exports.CategoriaLancamentoDropdownDto = exports.DropdownOptionDto = void 0;
class DropdownOptionDto {
    id;
    descricao;
}
exports.DropdownOptionDto = DropdownOptionDto;
class CategoriaLancamentoDropdownDto extends DropdownOptionDto {
    tipoCategoriaLancamento;
    taxa;
    diasParaCredito;
}
exports.CategoriaLancamentoDropdownDto = CategoriaLancamentoDropdownDto;
class PlanoContaDropdownDto extends DropdownOptionDto {
    eap;
}
exports.PlanoContaDropdownDto = PlanoContaDropdownDto;
class FornecedorDropdownDto extends DropdownOptionDto {
}
exports.FornecedorDropdownDto = FornecedorDropdownDto;
class ContaDropdownDto extends DropdownOptionDto {
}
exports.ContaDropdownDto = ContaDropdownDto;
class CentroCustoDropdownDto extends DropdownOptionDto {
}
exports.CentroCustoDropdownDto = CentroCustoDropdownDto;
//# sourceMappingURL=dropdown-options.dto.js.map