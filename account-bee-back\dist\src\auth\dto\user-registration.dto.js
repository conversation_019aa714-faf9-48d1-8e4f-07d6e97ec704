"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserRegistrationDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class UserRegistrationDto {
    name;
    email;
    password;
    telefone;
    empresaRazaoSocial;
    empresaTelefone;
}
exports.UserRegistrationDto = UserRegistrationDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome completo do usuário',
        example: 'João Silva',
        minLength: 2,
        maxLength: 100,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Nome é obrigatório' }),
    (0, class_validator_1.IsString)({ message: 'Nome deve ser uma string' }),
    (0, class_validator_1.MinLength)(2, { message: 'Nome deve ter no mínimo 2 caracteres' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Nome deve ter no máximo 100 caracteres' }),
    __metadata("design:type", String)
], UserRegistrationDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Email do usuário',
        example: '<EMAIL>',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Email é obrigatório' }),
    (0, class_validator_1.IsEmail)({}, { message: 'Email inválido' }),
    __metadata("design:type", String)
], UserRegistrationDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Senha do usuário',
        example: 'senha123',
        minLength: 6,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Senha é obrigatória' }),
    (0, class_validator_1.IsString)({ message: 'Senha deve ser uma string' }),
    (0, class_validator_1.MinLength)(6, { message: 'Senha deve ter no mínimo 6 caracteres' }),
    __metadata("design:type", String)
], UserRegistrationDto.prototype, "password", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Telefone do usuário',
        example: '(11) 99999-9999',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Telefone deve ser uma string' }),
    (0, class_validator_1.Matches)(/^\(\d{2}\)\s?\d{4,5}-?\d{4}$/, {
        message: 'Telefone deve estar no formato (XX) XXXXX-XXXX ou (XX) XXXX-XXXX',
    }),
    __metadata("design:type", String)
], UserRegistrationDto.prototype, "telefone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Razão social da empresa',
        example: 'Empresa Exemplo LTDA',
        minLength: 2,
        maxLength: 255,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Razão social da empresa é obrigatória' }),
    (0, class_validator_1.IsString)({ message: 'Razão social deve ser uma string' }),
    (0, class_validator_1.MinLength)(2, { message: 'Razão social deve ter no mínimo 2 caracteres' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Razão social deve ter no máximo 255 caracteres' }),
    __metadata("design:type", String)
], UserRegistrationDto.prototype, "empresaRazaoSocial", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Telefone da empresa',
        example: '(11) 3333-4444',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Telefone da empresa deve ser uma string' }),
    (0, class_validator_1.Matches)(/^\(\d{2}\)\s?\d{4,5}-?\d{4}$/, {
        message: 'Telefone da empresa deve estar no formato (XX) XXXXX-XXXX ou (XX) XXXX-XXXX',
    }),
    __metadata("design:type", String)
], UserRegistrationDto.prototype, "empresaTelefone", void 0);
//# sourceMappingURL=user-registration.dto.js.map