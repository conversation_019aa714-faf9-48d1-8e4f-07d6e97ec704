"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const usuario_entity_1 = require("../entities/usuario.entity");
const empresa_entity_1 = require("../entities/empresa.entity");
const local_entity_1 = require("../entities/local.entity");
const legacy_crypto_service_1 = require("../crypto/legacy-crypto.service");
const class_transformer_1 = require("class-transformer");
const user_response_dto_1 = require("./dto/user-response.dto");
const base_service_1 = require("../common/services/base.service");
let UsersService = class UsersService extends base_service_1.BaseService {
    usuarioRepository;
    empresaRepository;
    localRepository;
    cryptoService;
    constructor(usuarioRepository, empresaRepository, localRepository, cryptoService) {
        super();
        this.usuarioRepository = usuarioRepository;
        this.empresaRepository = empresaRepository;
        this.localRepository = localRepository;
        this.cryptoService = cryptoService;
    }
    async create(createUserDto, currentUser) {
        const encryptedEmail = this.cryptoService.encrypt(createUserDto.email, 0);
        if (!encryptedEmail) {
            throw new common_1.BadRequestException('Erro ao criptografar email');
        }
        const existingUser = await this.usuarioRepository.findOne({
            where: { email: encryptedEmail, isExcluido: 'N' },
        });
        if (existingUser) {
            throw new common_1.ConflictException('Email já cadastrado');
        }
        let empresaId = createUserDto.empresaId;
        if (!currentUser.admGeral || !empresaId) {
            empresaId = currentUser.empresaId;
        }
        const empresa = await this.empresaRepository.findOne({
            where: { id: empresaId },
            select: [
                'id',
                'nomeEmpresa',
                'razaoSocial',
                'telefone',
                'ddd',
                'horaFimExpediente',
                'statusEmpresa',
                'isExcluido',
            ],
        });
        if (!empresa) {
            throw new common_1.BadRequestException('Empresa não encontrada');
        }
        if (!currentUser.admGeral &&
            createUserDto.empresaId &&
            createUserDto.empresaId !== currentUser.empresaId) {
            throw new common_1.BadRequestException('Você só pode criar usuários para sua própria empresa');
        }
        const encryptedPassword = this.cryptoService.encrypt(createUserDto.senha, 0);
        if (!encryptedPassword) {
            throw new common_1.BadRequestException('Erro ao criptografar senha');
        }
        const now = new Date();
        const usuario = this.usuarioRepository.create({
            ...createUserDto,
            empresaId: empresaId,
            email: encryptedEmail,
            senha: encryptedPassword,
            isExcluido: 'N',
            dataHoraUsuarioInc: now,
            dataHoraUsuarioAlt: now,
            usuarioInc: currentUser.email || 'sistema',
            usuarioAlt: currentUser.email || 'sistema',
            admGeral: createUserDto.admGeral || false,
            admLocal: createUserDto.admLocal || false,
            operador: createUserDto.operador || false,
            operadorEstoque: createUserDto.operadorEstoque || false,
            visualizaSaldosBancarios: createUserDto.visualizaSaldosBancarios || false,
            gerenciarLctosFinanceiros: createUserDto.gerenciarLctosFinanceiros || false,
            relatoriosFinanceiros: createUserDto.relatoriosFinanceiros || false,
            relatoriosComandas: createUserDto.relatoriosComandas || false,
            gerenciarProdutos: createUserDto.gerenciarProdutos || false,
            podeAcessarFiscal: createUserDto.podeAcessarFiscal || false,
            podeAcessarConfiguracoes: createUserDto.podeAcessarConfiguracoes || false,
            twoAuthentication: createUserDto.twoAuthentication || false,
            podeVisualizarCatFinOculto: createUserDto.podeVisualizarCatFinOculto || false,
            desconectarUsuarios: createUserDto.desconectarUsuarios || false,
            podeGerarExcelFinanceiro: createUserDto.podeGerarExcelFinanceiro || false,
            podeImprimirLancamentosFinanceiro: createUserDto.podeImprimirLancamentosFinanceiro || false,
            usuarioLogado: false,
        });
        const savedUser = await this.usuarioRepository.save(usuario);
        return this.prepareUserResponse(savedUser);
    }
    async findAll(filterDto, currentUser) {
        const { page = 1, limit = 10, search, empresaId, status, admGeral, admLocal, } = filterDto;
        const where = {
            isExcluido: status || 'N',
        };
        if (!currentUser.admGeral) {
            where.empresaId = currentUser.empresaId;
        }
        else {
            if (!empresaId) {
                throw new common_1.BadRequestException('empresaId must be a number conforming to the specified constraints');
            }
            where.empresaId = empresaId;
        }
        if (admGeral !== undefined) {
            where.admGeral = admGeral;
        }
        if (admLocal !== undefined) {
            where.admLocal = admLocal;
        }
        const queryBuilder = this.usuarioRepository.createQueryBuilder('usuario');
        queryBuilder.where(where);
        if (search) {
            queryBuilder.andWhere('(usuario.nomeUsuario LIKE :search OR usuario.email LIKE :search)', { search: `%${search}%` });
        }
        const { page: validatedPage, limit: validatedLimit } = this.validatePaginationParams(page, limit);
        this.applyDefaultSorting(queryBuilder, 'usuario');
        this.applyPagination(queryBuilder, validatedPage, validatedLimit);
        const [users, total] = await queryBuilder.getManyAndCount();
        return this.createPaginatedResponse(users.map((user) => this.prepareUserResponse(user)), total, validatedPage, validatedLimit);
    }
    async findOne(id, currentUser) {
        const usuario = await this.usuarioRepository.findOne({
            where: { id, isExcluido: 'N' },
        });
        if (!usuario) {
            throw new common_1.NotFoundException('Usuário não encontrado');
        }
        if (!currentUser.admGeral && usuario.empresaId !== currentUser.empresaId) {
            throw new common_1.ForbiddenException('Sem permissão para visualizar este usuário');
        }
        return this.prepareUserResponse(usuario);
    }
    async update(id, updateUserDto, currentUser) {
        const usuario = await this.usuarioRepository.findOne({
            where: { id, isExcluido: 'N' },
        });
        if (!usuario) {
            throw new common_1.NotFoundException('Usuário não encontrado');
        }
        if (!currentUser.admGeral && usuario.empresaId !== currentUser.empresaId) {
            throw new common_1.ForbiddenException('Sem permissão para editar este usuário');
        }
        if (updateUserDto.email && updateUserDto.email !== usuario.email) {
            const encryptedEmail = this.cryptoService.encrypt(updateUserDto.email, 0);
            if (!encryptedEmail) {
                throw new common_1.BadRequestException('Erro ao criptografar email');
            }
            const existingUser = await this.usuarioRepository.findOne({
                where: { email: encryptedEmail, isExcluido: 'N', id: (0, typeorm_2.Not)(id) },
            });
            if (existingUser) {
                throw new common_1.ConflictException('Email já cadastrado');
            }
            updateUserDto.email = encryptedEmail;
        }
        Object.assign(usuario, updateUserDto);
        usuario.dataHoraUsuarioAlt = new Date();
        usuario.usuarioAlt = currentUser.email || 'sistema';
        const updatedUser = await this.usuarioRepository.save(usuario);
        return this.prepareUserResponse(updatedUser);
    }
    async updatePassword(id, updatePasswordDto, currentUser) {
        const usuario = await this.usuarioRepository.findOne({
            where: { id, isExcluido: 'N' },
        });
        if (!usuario) {
            throw new common_1.NotFoundException('Usuário não encontrado');
        }
        if (id !== currentUser.id &&
            !currentUser.admGeral &&
            usuario.empresaId !== currentUser.empresaId) {
            throw new common_1.ForbiddenException('Sem permissão para alterar senha deste usuário');
        }
        if (id === currentUser.id) {
            const senhaAtualCorreta = await this.cryptoService.compare(updatePasswordDto.senhaAtual, usuario.senha || '', 0);
            if (!senhaAtualCorreta) {
                throw new common_1.BadRequestException('Senha atual incorreta');
            }
        }
        const encryptedPassword = this.cryptoService.encrypt(updatePasswordDto.novaSenha, 0);
        if (!encryptedPassword) {
            throw new common_1.BadRequestException('Erro ao criptografar nova senha');
        }
        usuario.senha = encryptedPassword;
        usuario.dataHoraUsuarioAlt = new Date();
        usuario.usuarioAlt = currentUser.email || 'sistema';
        await this.usuarioRepository.save(usuario);
    }
    async remove(id, currentUser) {
        const usuario = await this.usuarioRepository.findOne({
            where: { id, isExcluido: 'N' },
        });
        if (!usuario) {
            throw new common_1.NotFoundException('Usuário não encontrado');
        }
        if (!currentUser.admGeral && usuario.empresaId !== currentUser.empresaId) {
            throw new common_1.ForbiddenException('Sem permissão para excluir este usuário');
        }
        if (id === currentUser.id) {
            throw new common_1.BadRequestException('Não é possível excluir o próprio usuário');
        }
        try {
            usuario.isExcluido = 'S';
            usuario.dataHoraUsuarioDel = new Date();
            usuario.usuarioDel = currentUser.email || 'sistema';
            await this.usuarioRepository.save(usuario);
        }
        catch (error) {
            throw new common_1.BadRequestException('Erro interno ao excluir usuário');
        }
    }
    async toggleStatus(id, currentUser) {
        const usuario = await this.usuarioRepository.findOne({
            where: { id },
        });
        if (!usuario) {
            throw new common_1.NotFoundException('Usuário não encontrado');
        }
        if (!currentUser.admGeral && usuario.empresaId !== currentUser.empresaId) {
            throw new common_1.ForbiddenException('Sem permissão para alterar status deste usuário');
        }
        usuario.isExcluido = usuario.isExcluido === 'N' ? 'S' : 'N';
        usuario.dataHoraUsuarioAlt = new Date();
        usuario.usuarioAlt = currentUser.email || 'sistema';
        const updatedUser = await this.usuarioRepository.save(usuario);
        return this.prepareUserResponse(updatedUser);
    }
    async findByEmpresa(empresaId, currentUser) {
        if (!currentUser.admGeral && empresaId !== currentUser.empresaId) {
            throw new common_1.ForbiddenException('Sem permissão para visualizar usuários desta empresa');
        }
        const queryBuilder = this.usuarioRepository.createQueryBuilder('usuario');
        queryBuilder.where({ empresaId, isExcluido: 'N' });
        this.applyDefaultSorting(queryBuilder, 'usuario');
        const users = await queryBuilder.getMany();
        return users.map((user) => this.prepareUserResponse(user));
    }
    prepareUserResponse(user) {
        const decryptedUser = { ...user };
        if (user.email) {
            if (user.email.includes('@') &&
                !user.email.includes('=') &&
                !user.email.includes('+') &&
                user.email.length < 100) {
                decryptedUser.email = user.email;
            }
            else {
                let decryptedEmail = null;
                const keysToTry = [
                    0,
                    user.empresaId || 0,
                    user.id,
                    1,
                    2,
                    3,
                    4,
                    5,
                    10,
                    20,
                    50,
                    100,
                    999,
                ];
                for (const key of keysToTry) {
                    decryptedEmail = this.cryptoService.decrypt(user.email, key);
                    if (decryptedEmail && decryptedEmail.includes('@')) {
                        break;
                    }
                }
                if (decryptedEmail && decryptedEmail.includes('@')) {
                    decryptedUser.email = decryptedEmail;
                }
                else {
                    const tempEmail = `usuario${user.id}@sistema.local`;
                    decryptedUser.email = tempEmail;
                }
            }
        }
        return (0, class_transformer_1.plainToClass)(user_response_dto_1.UserResponseDto, decryptedUser, {
            excludeExtraneousValues: true,
        });
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(usuario_entity_1.Usuario)),
    __param(1, (0, typeorm_1.InjectRepository)(empresa_entity_1.Empresa)),
    __param(2, (0, typeorm_1.InjectRepository)(local_entity_1.Local)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        legacy_crypto_service_1.LegacyCryptoService])
], UsersService);
//# sourceMappingURL=users.service.js.map