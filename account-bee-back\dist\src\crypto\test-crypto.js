"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const legacy_crypto_service_1 = require("./legacy-crypto.service");
async function testCrypto() {
    const cryptoService = new legacy_crypto_service_1.LegacyCryptoService();
    console.log('=== TESTE DE CRIPTOGRAFIA COMPATÍVEL COM JAVA ===\n');
    console.log('1. Teste de criptografia básica:');
    const testString = '<EMAIL>';
    const publicKey = 0;
    const encrypted = cryptoService.encrypt(testString, publicKey);
    console.log(`   Original: ${testString}`);
    console.log(`   Criptografado: ${encrypted}`);
    if (encrypted) {
        const decrypted = cryptoService.decrypt(encrypted, publicKey);
        console.log(`   Descriptografado: ${decrypted}`);
        console.log(`   ✓ Sucesso: ${testString === decrypted}\n`);
    }
    else {
        console.log(`   ✗ Erro na criptografia\n`);
    }
    console.log('2. Teste com valores do sistema legado:');
    console.log('   (Adicione aqui valores criptografados conhecidos do sistema Java para validar)\n');
    console.log('3. Teste com diferentes chaves públicas:');
    const publicKeys = [0, 1, 123, 999, 12345];
    for (const pk of publicKeys) {
        const encr = cryptoService.encrypt('teste', pk);
        if (encr) {
            const decr = cryptoService.decrypt(encr, pk);
            console.log(`   PublicKey ${pk}: ${encr.substring(0, 20)}... -> ${decr}`);
        }
        else {
            console.log(`   PublicKey ${pk}: Erro na criptografia`);
        }
    }
    console.log('\n4. Teste com caracteres especiais:');
    const specialChars = [
        'senha@123',
        'São Paulo',
        'José & Maria',
        'R$ 1.000,00',
    ];
    for (const str of specialChars) {
        const encr = cryptoService.encrypt(str, 0);
        if (encr) {
            const decr = cryptoService.decrypt(encr, 0);
            console.log(`   "${str}" -> OK: ${str === decr}`);
        }
        else {
            console.log(`   "${str}" -> Erro na criptografia`);
        }
    }
    console.log('\n=== FIM DOS TESTES ===');
}
testCrypto().catch(console.error);
//# sourceMappingURL=test-crypto.js.map