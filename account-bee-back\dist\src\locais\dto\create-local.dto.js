"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateLocalDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateLocalDto {
    nome;
    endereco;
    numero;
    bairro;
    cep;
    idEmpresa;
}
exports.CreateLocalDto = CreateLocalDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome/descrição do local',
        example: 'Loja Centro - São Paulo',
        maxLength: 100,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Nome do local é obrigatório' }),
    (0, class_validator_1.IsString)({ message: 'Nome do local deve ser uma string' }),
    (0, class_validator_1.MaxLength)(100, {
        message: 'Nome do local deve ter no máximo 100 caracteres',
    }),
    __metadata("design:type", String)
], CreateLocalDto.prototype, "nome", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Endereço do local',
        example: 'Rua das Flores, 123',
        required: false,
        maxLength: 255,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Endereço deve ser uma string' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Endereço deve ter no máximo 255 caracteres' }),
    __metadata("design:type", String)
], CreateLocalDto.prototype, "endereco", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Número do endereço',
        example: '123A',
        required: false,
        maxLength: 20,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Número deve ser uma string' }),
    (0, class_validator_1.MaxLength)(20, { message: 'Número deve ter no máximo 20 caracteres' }),
    __metadata("design:type", String)
], CreateLocalDto.prototype, "numero", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Bairro do local',
        example: 'Centro',
        required: false,
        maxLength: 100,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Bairro deve ser uma string' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Bairro deve ter no máximo 100 caracteres' }),
    __metadata("design:type", String)
], CreateLocalDto.prototype, "bairro", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'CEP do local',
        example: '12345-678',
        required: false,
        maxLength: 10,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'CEP deve ser uma string' }),
    (0, class_validator_1.Matches)(/^\d{5}-?\d{3}$/, {
        message: 'CEP deve estar no formato 12345-678 ou 12345678',
    }),
    (0, class_validator_1.MaxLength)(10, { message: 'CEP deve ter no máximo 10 caracteres' }),
    __metadata("design:type", String)
], CreateLocalDto.prototype, "cep", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da empresa à qual o local pertence (opcional, será inferida do usuário logado se não fornecida)',
        example: 1,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'ID da empresa deve ser um número' }),
    __metadata("design:type", Number)
], CreateLocalDto.prototype, "idEmpresa", void 0);
//# sourceMappingURL=create-local.dto.js.map