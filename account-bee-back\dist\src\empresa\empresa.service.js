"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmpresaService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const empresa_entity_1 = require("../entities/empresa.entity");
const legacy_crypto_service_1 = require("../crypto/legacy-crypto.service");
const uuid_1 = require("uuid");
let EmpresaService = class EmpresaService {
    empresaRepository;
    cryptoService;
    constructor(empresaRepository, cryptoService) {
        this.empresaRepository = empresaRepository;
        this.cryptoService = cryptoService;
    }
    async create(createEmpresaDto) {
        try {
            if (createEmpresaDto.nomeEmpresa) {
                const existingEmpresa = await this.empresaRepository.findOne({
                    where: { nomeEmpresa: createEmpresaDto.nomeEmpresa },
                    select: [
                        'id',
                        'nomeEmpresa',
                        'razaoSocial',
                        'telefone',
                        'ddd',
                        'horaFimExpediente',
                        'statusEmpresa',
                        'isExcluido',
                    ],
                });
                if (existingEmpresa) {
                    throw new common_1.ConflictException('Já existe uma empresa com este nome');
                }
            }
            const encryptedNomeEmpresa = this.cryptoService.encrypt(createEmpresaDto.nomeEmpresa, 0);
            if (!encryptedNomeEmpresa) {
                throw new common_1.BadRequestException('Erro ao criptografar nome da empresa');
            }
            let formattedTelefone = createEmpresaDto.telefone;
            if (formattedTelefone) {
                const numbers = formattedTelefone.replace(/\D/g, '');
                if (numbers.length >= 10) {
                    formattedTelefone = numbers.substring(2);
                }
                else {
                    formattedTelefone = numbers;
                }
            }
            const uuid = (0, uuid_1.v4)();
            const empresa = this.empresaRepository.create({
                nomeEmpresa: encryptedNomeEmpresa,
                razaoSocial: createEmpresaDto.razaoSocial,
                ddd: createEmpresaDto.ddd,
                telefone: formattedTelefone,
                horaFimExpediente: createEmpresaDto.horaFimExpediente || '23:59',
                uuid: uuid,
                isExcluido: 'N',
                dataHoraUsuarioInc: new Date(),
                dataHoraUsuarioAlt: new Date(),
                usuarioInc: 'sistema',
                usuarioAlt: 'sistema',
            });
            return await this.empresaRepository.save(empresa);
        }
        catch (error) {
            if (error instanceof common_1.ConflictException) {
                throw error;
            }
            console.error('Erro ao criar empresa:', error);
            throw new common_1.BadRequestException('Erro ao criar empresa');
        }
    }
    async findOne(id) {
        const empresa = await this.empresaRepository.findOne({
            where: { id, isExcluido: 'N' },
            select: [
                'id',
                'nomeEmpresa',
                'razaoSocial',
                'telefone',
                'ddd',
                'horaFimExpediente',
                'statusEmpresa',
                'isExcluido',
                'dataHoraUsuarioInc',
                'dataHoraUsuarioAlt',
                'usuarioInc',
                'usuarioAlt',
            ],
        });
        if (!empresa) {
            throw new common_1.NotFoundException('Empresa não encontrada');
        }
        return empresa;
    }
    async findAll() {
        return await this.empresaRepository.find({
            where: { isExcluido: 'N' },
            order: { nomeEmpresa: 'ASC' },
        });
    }
    async update(id, updateData) {
        const empresa = await this.findOne(id);
        if (updateData.nomeEmpresa &&
            updateData.nomeEmpresa !== empresa.nomeEmpresa) {
            const existingEmpresa = await this.empresaRepository.findOne({
                where: { nomeEmpresa: updateData.nomeEmpresa, isExcluido: 'N' },
                select: [
                    'id',
                    'nomeEmpresa',
                    'razaoSocial',
                    'telefone',
                    'ddd',
                    'horaFimExpediente',
                    'statusEmpresa',
                    'isExcluido',
                ],
            });
            if (existingEmpresa && existingEmpresa.id !== id) {
                throw new common_1.ConflictException('Já existe uma empresa com este nome');
            }
        }
        Object.assign(empresa, updateData);
        empresa.dataHoraUsuarioAlt = new Date();
        empresa.usuarioAlt = 'sistema';
        return await this.empresaRepository.save(empresa);
    }
    async remove(id) {
        const empresa = await this.findOne(id);
        empresa.isExcluido = 'S';
        empresa.dataHoraUsuarioDel = new Date();
        empresa.usuarioDel = 'sistema';
        await this.empresaRepository.save(empresa);
    }
};
exports.EmpresaService = EmpresaService;
exports.EmpresaService = EmpresaService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(empresa_entity_1.Empresa)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        legacy_crypto_service_1.LegacyCryptoService])
], EmpresaService);
//# sourceMappingURL=empresa.service.js.map