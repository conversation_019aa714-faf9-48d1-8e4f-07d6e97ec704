"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const legacy_crypto_service_1 = require("./src/crypto/legacy-crypto.service");
async function testCryptoWithRealValues() {
    const cryptoService = new legacy_crypto_service_1.LegacyCryptoService();
    console.log('=== TESTE COM VALORES DO BANCO ===\n');
    const testData = [
        { email: '<EMAIL>', senha: 'senha123' },
        { email: '<EMAIL>', senha: '123456' },
        { email: '<EMAIL>', senha: 'password' },
    ];
    console.log('Valores criptografados para teste:\n');
    for (const data of testData) {
        const emailCrypto = cryptoService.encrypt(data.email, 0);
        const senhaCrypto = cryptoService.encrypt(data.senha, 0);
        console.log(`Email: ${data.email}`);
        console.log(`  Criptografado: ${emailCrypto}`);
        console.log(`Senha: ${data.senha}`);
        console.log(`  Criptografado: ${senhaCrypto}`);
        console.log('---');
    }
    console.log('\nDescriptografando valores da requisição:');
    const encryptedEmail = 'cwczBrn0TMdiScSNRJNG1oHBwi0v3qvodZ8ukhCt8IA=';
    const encryptedSenha = 'zMEM+px9+MQng/Cnnt1muQ==';
    const decryptedEmail = cryptoService.decrypt(encryptedEmail, 0);
    const decryptedSenha = cryptoService.decrypt(encryptedSenha, 0);
    console.log(`Email criptografado: ${encryptedEmail}`);
    console.log(`Email descriptografado: ${decryptedEmail}`);
    console.log(`Senha criptografada: ${encryptedSenha}`);
    console.log(`Senha descriptografada: ${decryptedSenha}`);
}
testCryptoWithRealValues().catch(console.error);
//# sourceMappingURL=test-crypto-values.js.map