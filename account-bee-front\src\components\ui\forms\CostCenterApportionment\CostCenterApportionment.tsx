import { useState, useEffect, useCallback } from 'react';
import { Plus, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/forms';
import { Select, CurrencyInput } from '@/components/ui/forms';
import { Input } from '@/components/ui/forms';
import { Label } from '@/components/ui/forms';

const generateId = () => Math.random().toString(36).substr(2, 9);

export interface CostCenterAllocation {
  id: string;
  costCenterId: number;
  costCenterName: string;
  value: number;
  percentage: number;
}

interface CostCenterApportionmentProps {
  costCenters: any[]; // Array de centros de custo
  totalAmount: number;
  allocations: CostCenterAllocation[];
  onAllocationsChange: (allocations: CostCenterAllocation[]) => void;
  className?: string;
}

export function CostCenterApportionment({
  costCenters,
  totalAmount,
  allocations,
  onAllocationsChange,
  className = ''
}: CostCenterApportionmentProps) {

  const addAllocation = useCallback(() => {
    if (onAllocationsChange && typeof onAllocationsChange === 'function') {
      const newAllocation: CostCenterAllocation = {
        id: generateId(),
        costCenterId: 0,
        costCenterName: '',
        value: 0,
        percentage: 0,
      };
      onAllocationsChange([...allocations, newAllocation]);
    }
  }, [allocations, onAllocationsChange]);

  const removeAllocation = useCallback((id: string) => {
    if (allocations.length > 1 && onAllocationsChange && typeof onAllocationsChange === 'function') {
      onAllocationsChange(allocations.filter(allocation => allocation.id !== id));
    }
  }, [allocations, onAllocationsChange]);

  const updateAllocation = useCallback((id: string, field: keyof CostCenterAllocation, value: any) => {
    const updatedAllocations = allocations.map(allocation => {
      if (allocation.id === id) {
        const updated = { ...allocation, [field]: value };

        // Se mudou o centro de custo, atualizar o nome também
        if (field === 'costCenterId') {
          const costCenter = costCenters.find(center => center.id === value);
          updated.costCenterName = costCenter?.descricao || '';
        }

        // Cálculo automático entre valor e percentual
        if (field === 'value' && totalAmount > 0) {
          updated.percentage = (value / totalAmount) * 100;
        } else if (field === 'percentage' && totalAmount > 0) {
          updated.value = (value / 100) * totalAmount;
        }

        return updated;
      }
      return allocation;
    });

    if (onAllocationsChange && typeof onAllocationsChange === 'function') {
      onAllocationsChange(updatedAllocations);
    }
  }, [allocations, onAllocationsChange, costCenters, totalAmount]);

  // Inicializar com uma linha vazia se não houver alocações
  useEffect(() => {
    if (allocations.length === 0 && onAllocationsChange && typeof onAllocationsChange === 'function') {
      const newAllocation: CostCenterAllocation = {
        id: generateId(),
        costCenterId: 0,
        costCenterName: '',
        value: 0,
        percentage: 0,
      };
      onAllocationsChange([newAllocation]);
    }
  }, [allocations.length, onAllocationsChange]);

  const totalAllocatedValue = allocations.reduce((sum, allocation) => sum + (allocation.value || 0), 0);
  const totalAllocatedPercentage = allocations.reduce((sum, allocation) => sum + (allocation.percentage || 0), 0);
  const remainingValue = totalAmount - totalAllocatedValue;
  const remainingPercentage = 100 - totalAllocatedPercentage;

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <Label className="text-base font-medium">Alocação por Centro de Custo</Label>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={addAllocation}
          className="gap-2"
        >
          <Plus className="w-4 h-4" />
          Adicionar
        </Button>
      </div>

      <div className="space-y-3">
        {allocations.map((allocation, index) => (
          <div 
            key={allocation.id} 
            className="grid grid-cols-1 md:grid-cols-4 gap-3 p-4 border rounded-lg bg-gray-50"
          >
            <div>
              <Label className="text-xs text-gray-600 mb-1 block">Centro de Custo</Label>
              <Select
                value={allocation.costCenterId.toString()}
                onValueChange={(value) => updateAllocation(allocation.id, 'costCenterId', parseInt(value))}
                placeholder="Selecione o centro de custo"
                options={costCenters?.length > 0 ? costCenters.map(center => ({
                  value: center.id.toString(),
                  label: center.descricao,
                })) : [{ value: 'no-data', label: 'Nenhum centro de custo cadastrado', disabled: true }]}
              />
            </div>

            <div>
              <Label className="text-xs text-gray-600 mb-1 block">Valor</Label>
              <CurrencyInput
                value={allocation.value || 0}
                onChange={(value) => updateAllocation(allocation.id, 'value', value || 0)}
                placeholder="R$ 0,00"
              />
            </div>

            <div>
              <Label className="text-xs text-gray-600 mb-1 block">Percentual (%)</Label>
              <Input
                type="number"
                min="0"
                max="100"
                step="0.01"
                value={allocation.percentage || 0}
                onChange={(e) => updateAllocation(allocation.id, 'percentage', parseFloat(e.target.value) || 0)}
                placeholder="0,00"
              />
            </div>

            <div className="flex items-end">
              {allocations.length > 1 && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeAllocation(allocation.id)}
                  className="h-9 w-9 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Resumo da alocação */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <span className="font-medium text-blue-800">Valor Total:</span>
            <div className="text-blue-700">
              {new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
              }).format(totalAmount)}
            </div>
          </div>
          <div>
            <span className="font-medium text-blue-800">Alocado:</span>
            <div className="text-blue-700">
              {new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
              }).format(totalAllocatedValue)} ({totalAllocatedPercentage.toFixed(2)}%)
            </div>
          </div>
          <div>
            <span className="font-medium text-blue-800">Restante:</span>
            <div className={`${remainingValue < 0 ? 'text-red-600' : 'text-blue-700'}`}>
              {new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
              }).format(remainingValue)} ({remainingPercentage.toFixed(2)}%)
            </div>
          </div>
        </div>
        {remainingValue < 0 && (
          <div className="mt-2 text-xs text-red-600">
            ⚠️ O valor alocado excede o valor total da despesa
          </div>
        )}
      </div>
    </div>
  );
} 