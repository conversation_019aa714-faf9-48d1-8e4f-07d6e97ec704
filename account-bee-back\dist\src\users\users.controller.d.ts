import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UpdatePasswordDto } from './dto/update-password.dto';
import { UserResponseDto } from './dto/user-response.dto';
import { FilterUsersDto } from './dto/filter-users.dto';
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    create(createUserDto: CreateUserDto, req: any): Promise<UserResponseDto>;
    findAll(filterDto: FilterUsersDto, req: any): Promise<{
        data: UserResponseDto[];
        total: number;
        page: number;
        limit: number;
    }>;
    findByEmpresa(empresaId: string, req: any): Promise<UserResponseDto[]>;
    findOne(id: string, req: any): Promise<UserResponseDto>;
    update(id: string, updateUserDto: UpdateUserDto, req: any): Promise<UserResponseDto>;
    updatePassword(id: string, updatePasswordDto: UpdatePasswordDto, req: any): Promise<void>;
    toggleStatus(id: string, req: any): Promise<UserResponseDto>;
    remove(id: string, req: any): Promise<void>;
}
