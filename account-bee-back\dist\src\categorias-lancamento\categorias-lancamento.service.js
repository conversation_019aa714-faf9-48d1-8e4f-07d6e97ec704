"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CategoriasLancamentoService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const categoria_lancamento_entity_1 = require("../entities/categoria-lancamento.entity");
let CategoriasLancamentoService = class CategoriasLancamentoService {
    categoriaRepository;
    constructor(categoriaRepository) {
        this.categoriaRepository = categoriaRepository;
    }
    async findAllByEmpresa(empresaId) {
        console.log('🔍 CategoriasService - Buscando categorias para empresa:', empresaId);
        const categorias = await this.categoriaRepository.find({
            where: {
                empresaId,
                isExcluido: 'N',
            },
            order: {
                descricao: 'ASC',
            },
        });
        console.log('✅ CategoriasService - Encontradas', categorias.length, 'categorias');
        return categorias.map(categoria => ({
            id: categoria.id,
            descricao: categoria.descricao,
            tipoCategoriaLancamento: categoria.tipoCategoriaLancamento,
            taxa: categoria.taxa,
            diasParaCredito: categoria.diasParaCredito,
        }));
    }
    async findById(id, empresaId) {
        return this.categoriaRepository.findOne({
            where: {
                id,
                empresaId,
                isExcluido: 'N',
            },
        });
    }
};
exports.CategoriasLancamentoService = CategoriasLancamentoService;
exports.CategoriasLancamentoService = CategoriasLancamentoService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(categoria_lancamento_entity_1.CategoriaLancamento)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], CategoriasLancamentoService);
//# sourceMappingURL=categorias-lancamento.service.js.map