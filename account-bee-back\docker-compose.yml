version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: accountbee-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: accountbee
      MYSQL_USER: accountbee
      MYSQL_PASSWORD: accountbee123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password

volumes:
  mysql_data:
