"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.LegacyCryptoService = void 0;
const common_1 = require("@nestjs/common");
const crypto = __importStar(require("crypto"));
const iconv = __importStar(require("iconv-lite"));
let LegacyCryptoService = class LegacyCryptoService {
    PADDING_CHARACTERS = 'akfhdjJHy45775877';
    ALGORITHM = 'aes-128-ecb';
    doKey(publicKey) {
        const keyD = String(publicKey);
        const paddingLength = 16 - keyD.length;
        if (paddingLength > this.PADDING_CHARACTERS.length) {
            return '';
        }
        const keyDFinal = keyD + this.PADDING_CHARACTERS.substring(0, paddingLength);
        return keyDFinal;
    }
    doKeyPadded(publicKey) {
        const keyD = String(publicKey);
        let keyDFinal = keyD + this.PADDING_CHARACTERS.substring(0, 15 - keyD.length);
        if (keyDFinal.length < 16) {
            keyDFinal = keyDFinal + '0'.repeat(16 - keyDFinal.length);
        }
        else if (keyDFinal.length > 16) {
            keyDFinal = keyDFinal.substring(0, 16);
        }
        return keyDFinal;
    }
    encrypt(strToEncrypt, publicKey) {
        try {
            const keyD = String(publicKey);
            const paddingNeeded = 16 - keyD.length;
            if (paddingNeeded > this.PADDING_CHARACTERS.length) {
                return null;
            }
            const padding = this.PADDING_CHARACTERS.substring(0, paddingNeeded);
            const key = keyD + padding;
            if (key.length !== 16) {
                return null;
            }
            const keyBuffer = Buffer.from(key, 'utf8');
            const textBuffer = Buffer.from(strToEncrypt, 'utf8');
            const cipher = crypto.createCipheriv(this.ALGORITHM, keyBuffer, null);
            cipher.setAutoPadding(true);
            let encrypted = cipher.update(textBuffer);
            encrypted = Buffer.concat([encrypted, cipher.final()]);
            return encrypted.toString('base64');
        }
        catch (error) {
            return null;
        }
    }
    decrypt(strToDecrypt, publicKey) {
        const approaches = [
            { name: '15-byte key + null padding', keyMethod: 'doKey', padTo16: true },
            {
                name: '16-byte key with zeros',
                keyMethod: 'doKeyPadded',
                padTo16: false,
            },
            { name: '15-byte key as-is', keyMethod: 'doKey', padTo16: false },
        ];
        for (const approach of approaches) {
            try {
                const key = approach.keyMethod === 'doKey'
                    ? this.doKey(publicKey)
                    : this.doKeyPadded(publicKey);
                let keyBuffer = iconv.encode(key, 'ISO-8859-1');
                if (approach.padTo16 && keyBuffer.length === 15) {
                    keyBuffer = Buffer.concat([keyBuffer, Buffer.from([0])]);
                }
                if (keyBuffer.length !== 16)
                    continue;
                const encryptedBuffer = Buffer.from(strToDecrypt, 'base64');
                const decipher = crypto.createDecipheriv(this.ALGORITHM, keyBuffer, null);
                decipher.setAutoPadding(true);
                let decrypted = decipher.update(encryptedBuffer);
                decrypted = Buffer.concat([decrypted, decipher.final()]);
                const result = iconv.decode(decrypted, 'windows-1252');
                if (result && (result.includes('@') || result.length > 0)) {
                    return result;
                }
            }
            catch (error) {
                continue;
            }
        }
        return null;
    }
    async compare(plainPassword, encryptedPassword, publicKey) {
        try {
            const decrypted = this.decrypt(encryptedPassword, publicKey);
            return decrypted === plainPassword;
        }
        catch (error) {
            return false;
        }
    }
    testCompatibility() {
        const testString = '<EMAIL>';
        const publicKey = 0;
        const encrypted = this.encrypt(testString, publicKey);
        if (encrypted) {
            const decrypted = this.decrypt(encrypted, publicKey);
            console.log('Crypto test result:', testString === decrypted);
        }
        else {
            console.log('Crypto test failed: encryption returned null');
        }
    }
};
exports.LegacyCryptoService = LegacyCryptoService;
exports.LegacyCryptoService = LegacyCryptoService = __decorate([
    (0, common_1.Injectable)()
], LegacyCryptoService);
//# sourceMappingURL=legacy-crypto.service.js.map