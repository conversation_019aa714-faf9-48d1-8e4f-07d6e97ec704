import { CreateFornecedorDto } from './dto/create-fornecedor.dto';
import { UpdateFornecedorDto } from './dto/update-fornecedor.dto';
import { FornecedorRepository } from './fornecedor.repository';
export declare class FornecedorValidation {
    private readonly fornecedorRepository;
    constructor(fornecedorRepository: FornecedorRepository);
    validateCreate(createDto: CreateFornecedorDto, empresaId: number): Promise<void>;
    validateUpdate(id: number, updateDto: UpdateFornecedorDto, empresaId: number): Promise<void>;
    validateDelete(id: number, empresaId: number): Promise<void>;
    private validateCpf;
    private validateCnpj;
    private checkDescricaoDuplication;
    private checkCpfDuplication;
    private checkCnpjDuplication;
}
