"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePasswordDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class UpdatePasswordDto {
    senhaAtual;
    novaSenha;
}
exports.UpdatePasswordDto = UpdatePasswordDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Senha atual do usuário',
        example: 'senhaAtual123',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Senha atual é obrigatória' }),
    (0, class_validator_1.IsString)({ message: 'Senha atual deve ser uma string' }),
    __metadata("design:type", String)
], UpdatePasswordDto.prototype, "senhaAtual", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nova senha do usuário',
        example: 'novaSenha456',
        minLength: 6,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Nova senha é obrigatória' }),
    (0, class_validator_1.IsString)({ message: 'Nova senha deve ser uma string' }),
    (0, class_validator_1.MinLength)(6, { message: 'Nova senha deve ter no mínimo 6 caracteres' }),
    __metadata("design:type", String)
], UpdatePasswordDto.prototype, "novaSenha", void 0);
//# sourceMappingURL=update-password.dto.js.map