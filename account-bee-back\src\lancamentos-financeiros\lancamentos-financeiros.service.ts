import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { LancamentoFinanceiro } from '../entities/lancamento-financeiro.entity';
import { FinanceiroCentroCusto } from '../entities/financeiro-centro-custo.entity';
import { FinancialCryptoService } from '../crypto/financial-crypto.service';
import { 
  CreateLancamentoFinanceiroDto, 
  LancamentoFinanceiroResponseDto,
  CostCenterAllocationDto 
} from './dto';

@Injectable()
export class LancamentosFinanceirosService {
  constructor(
    @InjectRepository(LancamentoFinanceiro)
    private readonly lancamentoRepository: Repository<LancamentoFinanceiro>,
    @InjectRepository(FinanceiroCentroCusto)
    private readonly financeiroCentroCustoRepository: Repository<FinanceiroCentroCusto>,
    private readonly financialCryptoService: FinancialCryptoService,
    private readonly dataSource: DataSource,
  ) {}

  async create(
    createDto: CreateLancamentoFinanceiroDto,
    user: any,
  ): Promise<LancamentoFinanceiroResponseDto> {
    const empresaId = user.empresaId;
    
    // Validações obrigatórias
    this.validateRequiredFields(createDto, empresaId);
    
    // Validar alocações de centro de custo se fornecidas
    if (createDto.alocacoesCentroCusto && createDto.alocacoesCentroCusto.length > 0) {
      this.validateCostCenterAllocations(createDto.alocacoesCentroCusto, createDto.valor);
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const now = new Date();
      const userInfo = JSON.stringify({
        id: user.id,
        nomeUsuario: user.name || user.email,
        email: user.email,
      });

      // Criptografar valores
      const valorCriptografado = this.financialCryptoService.encryptValue(createDto.valor, empresaId);
      const valorBrutoCriptografado = createDto.valorBruto 
        ? this.financialCryptoService.encryptValue(createDto.valorBruto, empresaId)
        : null;

      // Gerar identificador único para o lançamento
      const timestamp = now.getTime();
      const identificador = `REC-${empresaId}-${timestamp}`;

      // Gerar UUID único
      const uuid = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;

      // Criar lançamento financeiro
      const lancamentoData: Partial<LancamentoFinanceiro> = {
        descricao: createDto.descricao,
        dataLancamento: createDto.dataLancamento ? new Date(createDto.dataLancamento) : undefined,
        dataCompetencia: createDto.dataCompetencia ? new Date(createDto.dataCompetencia) : undefined,
        valorBruto: valorBrutoCriptografado || undefined,
        valor: valorCriptografado,
        observacao: createDto.observacao,
        identificador,
        uuid,
        empresaId,
        pessoaId: createDto.pessoaId,
        contaId: createDto.contaId,
        localId: createDto.localId,
        fornecedorId: createDto.fornecedorId,
        tipoLancamentoFinanceiroId: 1, // Sempre 1 para receitas
        categoriaLctoFinanceiroId: createDto.categoriaLctoFinanceiroId,
        planoContaCredito: createDto.planoContaCredito,
        efetivado: false,
        conciliado: false,
        dataHoraUsuarioInc: now,
        dataHoraUsuarioAlt: now,
        usuarioInc: userInfo,
        usuarioAlt: userInfo,
        isExcluido: 'N',
      };

      const lancamento = queryRunner.manager.create(LancamentoFinanceiro, lancamentoData);

      const savedLancamento = await queryRunner.manager.save(LancamentoFinanceiro, lancamento);

      // Criar alocações de centro de custo se fornecidas
      if (createDto.alocacoesCentroCusto && createDto.alocacoesCentroCusto.length > 0) {
        await this.createCostCenterAllocations(
          queryRunner,
          savedLancamento.id,
          createDto.alocacoesCentroCusto,
          empresaId,
          userInfo,
          now,
        );
      }

      // Criar lançamentos recorrentes se solicitado
      if (createDto.repetirReceita && createDto.periodicidade && createDto.quantidadeRepeticoes) {
        await this.createRecurringLancamentos(
          queryRunner,
          savedLancamento,
          createDto,
          empresaId,
          userInfo,
        );
      }

      await queryRunner.commitTransaction();

      return this.mapToResponseDto(savedLancamento, empresaId);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  private validateRequiredFields(createDto: CreateLancamentoFinanceiroDto, empresaId: number): void {
    if (!empresaId) {
      throw new BadRequestException('EMPRESA_ID é obrigatório');
    }

    if (!createDto.pessoaId) {
      throw new BadRequestException('Cliente/Pessoa é obrigatório');
    }

    if (!createDto.descricao || createDto.descricao.trim().length === 0) {
      throw new BadRequestException('Descrição é obrigatória');
    }

    if (!createDto.valor || createDto.valor <= 0) {
      throw new BadRequestException('Valor líquido deve ser maior que zero');
    }
  }

  private validateCostCenterAllocations(
    allocations: CostCenterAllocationDto[],
    totalValue: number,
  ): void {
    // Validar soma dos percentuais = 100%
    const totalPercentage = allocations.reduce((sum, allocation) => {
      return sum + (allocation.porcentagem || 0);
    }, 0);

    if (Math.abs(totalPercentage - 100) > 0.01) {
      throw new BadRequestException('A soma dos percentuais de alocação deve ser igual a 100%');
    }

    // Validar soma dos valores = valor líquido
    const totalAllocatedValue = allocations.reduce((sum, allocation) => {
      return sum + allocation.valor;
    }, 0);

    if (Math.abs(totalAllocatedValue - totalValue) > 0.01) {
      throw new BadRequestException('A soma dos valores de alocação deve ser igual ao valor líquido');
    }

    // Validar que todos os centros de custo são únicos
    const centroCustoIds = allocations.map(a => a.centroCustoId);
    const uniqueIds = new Set(centroCustoIds);
    if (uniqueIds.size !== centroCustoIds.length) {
      throw new BadRequestException('Não é possível alocar o mesmo centro de custo múltiplas vezes');
    }
  }

  private async createCostCenterAllocations(
    queryRunner: any,
    lancamentoId: number,
    allocations: CostCenterAllocationDto[],
    empresaId: number,
    userInfo: string,
    now: Date,
  ): Promise<void> {
    for (const allocation of allocations) {
      const valorCriptografado = this.financialCryptoService.encryptValue(allocation.valor, empresaId);

      // Gerar UUID único para a alocação
      const uuidAlocacao = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}-${allocation.centroCustoId}`;

      const financeiroCentroCusto = queryRunner.manager.create(FinanceiroCentroCusto, {
        lancamentoFinanceiroId: lancamentoId,
        centroCustoId: allocation.centroCustoId,
        valor: valorCriptografado,
        porcentagem: allocation.porcentagem,
        uuid: uuidAlocacao,
        dataHoraUsuarioInc: now,
        dataHoraUsuarioAlt: now,
        usuarioInc: userInfo,
        usuarioAlt: userInfo,
        isExcluido: 'N',
      });

      await queryRunner.manager.save(FinanceiroCentroCusto, financeiroCentroCusto);
    }
  }

  private mapToResponseDto(
    lancamento: LancamentoFinanceiro,
    empresaId: number,
  ): LancamentoFinanceiroResponseDto {
    return {
      id: lancamento.id,
      descricao: lancamento.descricao,
      dataLancamento: lancamento.dataLancamento,
      dataCompetencia: lancamento.dataCompetencia,
      valorBruto: lancamento.valorBruto
        ? this.financialCryptoService.decryptValueAsNumber(lancamento.valorBruto, empresaId)
        : undefined,
      valor: lancamento.valor
        ? this.financialCryptoService.decryptValueAsNumber(lancamento.valor, empresaId)
        : 0,
      observacao: lancamento.observacao,
      efetivado: lancamento.efetivado,
      conciliado: lancamento.conciliado,
      empresaId: lancamento.empresaId,
      pessoaId: lancamento.pessoaId,
      contaId: lancamento.contaId,
      localId: lancamento.localId,
      fornecedorId: lancamento.fornecedorId,
      tipoLancamentoFinanceiroId: lancamento.tipoLancamentoFinanceiroId,
      categoriaLctoFinanceiroId: lancamento.categoriaLctoFinanceiroId,
      planoContaCredito: lancamento.planoContaCredito,
      dataHoraUsuarioInc: lancamento.dataHoraUsuarioInc,
      dataHoraUsuarioAlt: lancamento.dataHoraUsuarioAlt,
      usuarioInc: lancamento.usuarioInc,
      usuarioAlt: lancamento.usuarioAlt,
    };
  }

  async findById(id: number, empresaId: number): Promise<LancamentoFinanceiroResponseDto> {
    const lancamento = await this.lancamentoRepository.findOne({
      where: {
        id,
        empresaId,
        isExcluido: 'N',
      },
      relations: ['financeiroCentroCustos', 'financeiroCentroCustos.centroCusto'],
    });

    if (!lancamento) {
      throw new NotFoundException('Lançamento financeiro não encontrado');
    }

    const responseDto = this.mapToResponseDto(lancamento, empresaId);

    // Adicionar alocações de centro de custo
    if (lancamento.financeiroCentroCustos && lancamento.financeiroCentroCustos.length > 0) {
      responseDto.alocacoesCentroCusto = lancamento.financeiroCentroCustos
        .filter(fcc => fcc.isExcluido === 'N')
        .map(fcc => ({
          id: fcc.id,
          centroCustoId: fcc.centroCustoId,
          centroCustoNome: fcc.centroCusto?.descricao || '',
          valor: this.financialCryptoService.decryptValueAsNumber(fcc.valor, empresaId),
          porcentagem: fcc.porcentagem,
        }));
    }

    return responseDto;
  }

  async updateCostCenterAllocations(
    lancamentoId: number,
    allocations: CostCenterAllocationDto[],
    empresaId: number,
    user: any,
  ): Promise<void> {
    // Validar que o lançamento existe e pertence à empresa
    const lancamento = await this.lancamentoRepository.findOne({
      where: { id: lancamentoId, empresaId, isExcluido: 'N' },
    });

    if (!lancamento) {
      throw new NotFoundException('Lançamento financeiro não encontrado');
    }

    // Obter valor líquido do lançamento
    const valorLiquido = lancamento.valor
      ? this.financialCryptoService.decryptValueAsNumber(lancamento.valor, empresaId)
      : 0;

    // Validar alocações
    this.validateCostCenterAllocations(allocations, valorLiquido);

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Excluir alocações existentes (soft delete)
      await queryRunner.manager.update(
        FinanceiroCentroCusto,
        { lancamentoFinanceiroId: lancamentoId, isExcluido: 'N' },
        {
          isExcluido: 'S',
          dataHoraUsuarioDel: new Date(),
          usuarioDel: JSON.stringify({
            id: user.id,
            nomeUsuario: user.name || user.email,
            email: user.email,
          }),
        },
      );

      // Criar novas alocações
      const now = new Date();
      const userInfo = JSON.stringify({
        id: user.id,
        nomeUsuario: user.name || user.email,
        email: user.email,
      });

      await this.createCostCenterAllocations(
        queryRunner,
        lancamentoId,
        allocations,
        empresaId,
        userInfo,
        now,
      );

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async deleteCostCenterAllocation(
    lancamentoId: number,
    allocationId: number,
    empresaId: number,
    user: any,
  ): Promise<void> {
    const allocation = await this.financeiroCentroCustoRepository.findOne({
      where: {
        id: allocationId,
        lancamentoFinanceiroId: lancamentoId,
        isExcluido: 'N',
      },
      relations: ['lancamentoFinanceiro'],
    });

    // Verificar se o lançamento pertence à empresa do usuário
    if (allocation && allocation.lancamentoFinanceiro.empresaId !== empresaId) {
      throw new NotFoundException('Alocação de centro de custo não encontrada');
    }

    if (!allocation) {
      throw new NotFoundException('Alocação de centro de custo não encontrada');
    }

    // Soft delete
    allocation.isExcluido = 'S';
    allocation.dataHoraUsuarioDel = new Date();
    allocation.usuarioDel = JSON.stringify({
      id: user.id,
      nomeUsuario: user.name || user.email,
      email: user.email,
    });

    await this.financeiroCentroCustoRepository.save(allocation);
  }

  async getCostCenterAllocations(
    lancamentoId: number,
    empresaId: number,
  ): Promise<any[]> {
    const allocations = await this.financeiroCentroCustoRepository.find({
      where: {
        lancamentoFinanceiroId: lancamentoId,
        isExcluido: 'N',
      },
      relations: ['centroCusto', 'lancamentoFinanceiro'],
      order: { id: 'ASC' },
    });

    // Filtrar apenas alocações da empresa do usuário
    const filteredAllocations = allocations.filter(
      allocation => allocation.lancamentoFinanceiro.empresaId === empresaId
    );

    return filteredAllocations.map(allocation => ({
      id: allocation.id,
      centroCustoId: allocation.centroCustoId,
      centroCustoNome: allocation.centroCusto?.descricao || '',
      valor: this.financialCryptoService.decryptValueAsNumber(allocation.valor, empresaId),
      porcentagem: allocation.porcentagem,
    }));
  }

  private async createRecurringLancamentos(
    queryRunner: any,
    originalLancamento: LancamentoFinanceiro,
    createDto: CreateLancamentoFinanceiroDto,
    empresaId: number,
    userInfo: string,
  ): Promise<void> {
    const quantidadeRepeticoes = createDto.quantidadeRepeticoes || 1;

    for (let i = 1; i <= quantidadeRepeticoes; i++) {
      const nextDate = this.calculateNextDate(
        originalLancamento.dataLancamento || new Date(),
        createDto.periodicidade!,
        i,
      );

      const nextCompetenceDate = originalLancamento.dataCompetencia
        ? this.calculateNextDate(originalLancamento.dataCompetencia, createDto.periodicidade!, i)
        : null;

      const now = new Date();

      // Gerar identificador único para o lançamento recorrente
      const timestamp = now.getTime();
      const identificadorRecorrente = `REC-${empresaId}-${timestamp}-${i}`;

      // Gerar UUID único para lançamento recorrente
      const uuidRecorrente = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}-${i}`;

      // Criar novo lançamento
      const novoLancamentoData: Partial<LancamentoFinanceiro> = {
        descricao: originalLancamento.descricao,
        dataLancamento: nextDate,
        dataCompetencia: nextCompetenceDate || undefined,
        valorBruto: originalLancamento.valorBruto || undefined,
        valor: originalLancamento.valor,
        observacao: originalLancamento.observacao,
        identificador: identificadorRecorrente,
        uuid: uuidRecorrente,
        empresaId,
        pessoaId: originalLancamento.pessoaId,
        contaId: originalLancamento.contaId,
        localId: originalLancamento.localId,
        fornecedorId: originalLancamento.fornecedorId,
        tipoLancamentoFinanceiroId: 1, // Sempre 1 para receitas
        categoriaLctoFinanceiroId: originalLancamento.categoriaLctoFinanceiroId,
        planoContaCredito: originalLancamento.planoContaCredito,
        efetivado: false,
        conciliado: false,
        dataHoraUsuarioInc: now,
        dataHoraUsuarioAlt: now,
        usuarioInc: userInfo,
        usuarioAlt: userInfo,
        isExcluido: 'N',
      };

      const novoLancamento = queryRunner.manager.create(LancamentoFinanceiro, novoLancamentoData);

      const savedNovoLancamento = await queryRunner.manager.save(LancamentoFinanceiro, novoLancamento);

      // Criar alocações de centro de custo para o novo lançamento
      if (createDto.alocacoesCentroCusto && createDto.alocacoesCentroCusto.length > 0) {
        await this.createCostCenterAllocations(
          queryRunner,
          savedNovoLancamento.id,
          createDto.alocacoesCentroCusto,
          empresaId,
          userInfo,
          now,
        );
      }
    }
  }

  private calculateNextDate(baseDate: Date, periodicidade: string, increment: number): Date {
    const nextDate = new Date(baseDate);

    switch (periodicidade) {
      case 'mensal':
        nextDate.setMonth(nextDate.getMonth() + increment);
        break;
      case 'semanal':
        nextDate.setDate(nextDate.getDate() + (7 * increment));
        break;
      case 'anual':
        nextDate.setFullYear(nextDate.getFullYear() + increment);
        break;
      default:
        throw new BadRequestException('Periodicidade inválida');
    }

    return nextDate;
  }

  async findAll(filters: {
    empresaId: number;
    tipo?: number;
    page: number;
    limit: number;
    dataInicio?: Date;
    dataFim?: Date;
  }) {
    const { empresaId, tipo, page, limit, dataInicio, dataFim } = filters;

    const queryBuilder = this.lancamentoRepository
      .createQueryBuilder('lancamento')
      .leftJoinAndSelect('lancamento.pessoa', 'pessoa')
      .leftJoinAndSelect('lancamento.conta', 'conta')
      .leftJoinAndSelect('lancamento.local', 'local')
      .leftJoinAndSelect('lancamento.categoriaLctoFinanceiro', 'categoria')
      .leftJoinAndSelect('lancamento.tipoLancamentoFinanceiro', 'tipo')
      .where('lancamento.empresaId = :empresaId', { empresaId })
      .andWhere('lancamento.isExcluido = :isExcluido', { isExcluido: 'N' });

    if (tipo) {
      queryBuilder.andWhere('lancamento.tipoLancamentoFinanceiroId = :tipo', { tipo });
    }

    if (dataInicio) {
      queryBuilder.andWhere('lancamento.dataLancamento >= :dataInicio', { dataInicio });
    }

    if (dataFim) {
      queryBuilder.andWhere('lancamento.dataLancamento <= :dataFim', { dataFim });
    }

    // Contar total de registros
    const total = await queryBuilder.getCount();

    // Aplicar paginação
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    // Ordenar por data de lançamento (mais recente primeiro)
    queryBuilder.orderBy('lancamento.dataLancamento', 'DESC');

    const lancamentos = await queryBuilder.getMany();

    // Descriptografar valores
    const lancamentosDecriptografados = lancamentos.map(lancamento => ({
      ...lancamento,
      valor: lancamento.valor
        ? this.financialCryptoService.decryptValueAsNumber(lancamento.valor, empresaId)
        : 0,
      valorBruto: lancamento.valorBruto
        ? this.financialCryptoService.decryptValueAsNumber(lancamento.valorBruto, empresaId)
        : undefined,
    }));

    return {
      data: lancamentosDecriptografados,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getResumoFinanceiro(filters: {
    empresaId: number;
    dataInicio?: Date;
    dataFim?: Date;
  }) {
    const { empresaId, dataInicio, dataFim } = filters;

    // Buscar todos os lançamentos e descriptografar no código
    const queryBuilder = this.lancamentoRepository
      .createQueryBuilder('lancamento')
      .select([
        'lancamento.id',
        'lancamento.tipoLancamentoFinanceiroId',
        'lancamento.valor'
      ])
      .where('lancamento.empresaId = :empresaId', { empresaId })
      .andWhere('lancamento.isExcluido = :isExcluido', { isExcluido: 'N' });

    if (dataInicio) {
      queryBuilder.andWhere('lancamento.dataLancamento >= :dataInicio', { dataInicio });
    }

    if (dataFim) {
      queryBuilder.andWhere('lancamento.dataLancamento <= :dataFim', { dataFim });
    }

    const lancamentos = await queryBuilder.getMany();

    let totalReceitas = 0;
    let totalDespesas = 0;
    let quantidadeReceitas = 0;
    let quantidadeDespesas = 0;

    // Descriptografar e calcular totais
    lancamentos.forEach(lancamento => {
      const valor = lancamento.valor
        ? this.financialCryptoService.decryptValueAsNumber(lancamento.valor, empresaId)
        : 0;

      if (lancamento.tipoLancamentoFinanceiroId === 1) { // Receita
        totalReceitas += valor;
        quantidadeReceitas++;
      } else if (lancamento.tipoLancamentoFinanceiroId === 2) { // Despesa
        totalDespesas += valor;
        quantidadeDespesas++;
      }
    });

    return {
      totalReceitas,
      totalDespesas,
      saldo: totalReceitas - totalDespesas,
      quantidadeReceitas,
      quantidadeDespesas,
    };
  }
}
