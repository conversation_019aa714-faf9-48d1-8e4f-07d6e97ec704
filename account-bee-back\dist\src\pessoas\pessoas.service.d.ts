import { Repository } from 'typeorm';
import { Pessoa } from '../entities/pessoa.entity';
import { Empresa } from '../entities/empresa.entity';
import { Usuario } from '../entities/usuario.entity';
import { CreatePessoaDto } from './dto/create-pessoa.dto';
import { UpdatePessoaDto } from './dto/update-pessoa.dto';
import { FilterPessoasDto } from './dto/filter-pessoas.dto';
import { PessoaResponseDto } from './dto/pessoa-response.dto';
import { PessoaCryptoService } from '../crypto/pessoa-crypto.service';
import { BaseService } from '../common/services/base.service';
export declare class PessoasService extends BaseService {
    private readonly pessoaRepository;
    private readonly empresaRepository;
    private readonly cryptoService;
    constructor(pessoaRepository: Repository<Pessoa>, empresaRepository: Repository<Empresa>, cryptoService: PessoaCryptoService);
    create(createPessoaDto: CreatePessoaDto, currentUser: Usuario): Promise<PessoaResponseDto>;
    findAll(filterDto: FilterPessoasDto, currentUser: Usuario): Promise<{
        data: PessoaResponseDto[];
        total: number;
        page: number;
        limit: number;
    }>;
    findOne(id: number, currentUser: Usuario): Promise<PessoaResponseDto>;
    update(id: number, updatePessoaDto: UpdatePessoaDto, currentUser: Usuario): Promise<PessoaResponseDto>;
    remove(id: number, currentUser: Usuario): Promise<void>;
    toggleStatus(id: number, currentUser: Usuario): Promise<PessoaResponseDto>;
    findByEmpresa(empresaId: number, currentUser: Usuario): Promise<PessoaResponseDto[]>;
    private checkDuplicates;
    private encryptSensitiveData;
    private preparePessoaResponse;
    private decryptField;
    private normalizeSensitiveData;
}
