"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FornecedorValidation = void 0;
const common_1 = require("@nestjs/common");
const fornecedor_repository_1 = require("./fornecedor.repository");
let FornecedorValidation = class FornecedorValidation {
    fornecedorRepository;
    constructor(fornecedorRepository) {
        this.fornecedorRepository = fornecedorRepository;
    }
    async validateCreate(createDto, empresaId) {
        console.log('🔍 Validation - Validando criação de fornecedor');
        if (!createDto.descricao || createDto.descricao.trim().length === 0) {
            throw new common_1.BadRequestException('Descrição é obrigatória');
        }
        await this.checkDescricaoDuplication(createDto.descricao, empresaId);
        if (createDto.cpf) {
            if (!this.validateCpf(createDto.cpf)) {
                throw new common_1.BadRequestException('CPF inválido');
            }
            await this.checkCpfDuplication(createDto.cpf, empresaId);
        }
        if (createDto.cnpj) {
            if (!this.validateCnpj(createDto.cnpj)) {
                throw new common_1.BadRequestException('CNPJ inválido');
            }
            await this.checkCnpjDuplication(createDto.cnpj, empresaId);
        }
        if (!createDto.cpf && !createDto.cnpj) {
            throw new common_1.BadRequestException('CPF ou CNPJ deve ser fornecido');
        }
        console.log('✅ Validation - Validações de criação OK');
    }
    async validateUpdate(id, updateDto, empresaId) {
        console.log('🔍 Validation - Validando atualização de fornecedor');
        if (updateDto.descricao !== undefined) {
            if (!updateDto.descricao || updateDto.descricao.trim().length === 0) {
                throw new common_1.BadRequestException('Descrição é obrigatória');
            }
            await this.checkDescricaoDuplication(updateDto.descricao, empresaId, id);
        }
        if (updateDto.cpf !== undefined) {
            if (updateDto.cpf && !this.validateCpf(updateDto.cpf)) {
                throw new common_1.BadRequestException('CPF inválido');
            }
            if (updateDto.cpf) {
                await this.checkCpfDuplication(updateDto.cpf, empresaId, id);
            }
        }
        if (updateDto.cnpj !== undefined) {
            if (updateDto.cnpj && !this.validateCnpj(updateDto.cnpj)) {
                throw new common_1.BadRequestException('CNPJ inválido');
            }
            if (updateDto.cnpj) {
                await this.checkCnpjDuplication(updateDto.cnpj, empresaId, id);
            }
        }
        console.log('✅ Validation - Validações de atualização OK');
    }
    async validateDelete(id, empresaId) {
        console.log('🔍 Validation - Validando exclusão de fornecedor');
        console.log('✅ Validation - Validações de exclusão OK');
    }
    validateCpf(cpf) {
        const cleanCpf = cpf.replace(/\D/g, '');
        if (cleanCpf.length !== 11) {
            return false;
        }
        if (/^(\d)\1{10}$/.test(cleanCpf)) {
            return false;
        }
        let sum = 0;
        for (let i = 0; i < 9; i++) {
            sum += parseInt(cleanCpf.charAt(i)) * (10 - i);
        }
        let remainder = (sum * 10) % 11;
        if (remainder === 10)
            remainder = 0;
        if (remainder !== parseInt(cleanCpf.charAt(9))) {
            return false;
        }
        sum = 0;
        for (let i = 0; i < 10; i++) {
            sum += parseInt(cleanCpf.charAt(i)) * (11 - i);
        }
        remainder = (sum * 10) % 11;
        if (remainder === 10)
            remainder = 0;
        if (remainder !== parseInt(cleanCpf.charAt(10))) {
            return false;
        }
        return true;
    }
    validateCnpj(cnpj) {
        const cleanCnpj = cnpj.replace(/\D/g, '');
        if (cleanCnpj.length !== 14) {
            return false;
        }
        if (/^(\d)\1{13}$/.test(cleanCnpj)) {
            return false;
        }
        let sum = 0;
        const weights1 = [5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
        for (let i = 0; i < 12; i++) {
            sum += parseInt(cleanCnpj.charAt(i)) * weights1[i];
        }
        let remainder = sum % 11;
        const digit1 = remainder < 2 ? 0 : 11 - remainder;
        if (digit1 !== parseInt(cleanCnpj.charAt(12))) {
            return false;
        }
        sum = 0;
        const weights2 = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
        for (let i = 0; i < 13; i++) {
            sum += parseInt(cleanCnpj.charAt(i)) * weights2[i];
        }
        remainder = sum % 11;
        const digit2 = remainder < 2 ? 0 : 11 - remainder;
        if (digit2 !== parseInt(cleanCnpj.charAt(13))) {
            return false;
        }
        return true;
    }
    async checkDescricaoDuplication(descricao, empresaId, excludeId) {
        const existing = await this.fornecedorRepository.findByDescricao(descricao, empresaId);
        if (existing && existing.id !== excludeId) {
            throw new common_1.BadRequestException(`Já existe um fornecedor com a descrição '${descricao}' para esta empresa`);
        }
    }
    async checkCpfDuplication(cpf, empresaId, excludeId) {
        const existing = await this.fornecedorRepository.findByCpf(cpf, empresaId);
        if (existing && existing.id !== excludeId) {
            throw new common_1.BadRequestException(`Já existe um fornecedor com o CPF '${cpf}' para esta empresa`);
        }
    }
    async checkCnpjDuplication(cnpj, empresaId, excludeId) {
        const existing = await this.fornecedorRepository.findByCnpj(cnpj, empresaId);
        if (existing && existing.id !== excludeId) {
            throw new common_1.BadRequestException(`Já existe um fornecedor com o CNPJ '${cnpj}' para esta empresa`);
        }
    }
};
exports.FornecedorValidation = FornecedorValidation;
exports.FornecedorValidation = FornecedorValidation = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [fornecedor_repository_1.FornecedorRepository])
], FornecedorValidation);
//# sourceMappingURL=fornecedor.validation.js.map