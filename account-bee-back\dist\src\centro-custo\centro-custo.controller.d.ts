import { CentroCustoService } from './centro-custo.service';
import { CreateCentroCustoDto } from './dto/create-centro-custo.dto';
import { UpdateCentroCustoDto } from './dto/update-centro-custo.dto';
import { FilterCentroCustoDto } from './dto/filter-centro-custo.dto';
import { CentroCustoResponseDto } from './dto/centro-custo-response.dto';
import { CentroCustoDropdownDto } from '../lancamentos-financeiros/dto';
export declare class CentroCustoController {
    private readonly centroCustoService;
    constructor(centroCustoService: CentroCustoService);
    create(createCentroCustoDto: CreateCentroCustoDto, req: any): Promise<CentroCustoResponseDto>;
    getDropdownOptions(req: any): Promise<CentroCustoDropdownDto[]>;
    healthCheck(): {
        status: string;
        module: string;
        timestamp: string;
    };
    findAll(req: any, filterDto: FilterCentroCustoDto): Promise<CentroCustoResponseDto[]>;
    findOne(id: string, req: any): Promise<CentroCustoResponseDto>;
    update(id: string, updateCentroCustoDto: UpdateCentroCustoDto, req: any): Promise<CentroCustoResponseDto>;
    remove(id: string, req: any): Promise<void>;
}
