import { ContasService } from './contas.service';
import { CreateContaDto } from './dto/create-conta.dto';
import { UpdateContaDto } from './dto/update-conta.dto';
import { FilterContasDto } from './dto/filter-contas.dto';
import { ContaResponseDto } from './dto/conta-response.dto';
import { ContaDropdownDto } from '../lancamentos-financeiros/dto';
export declare class ContasController {
    private readonly contasService;
    constructor(contasService: ContasService);
    create(createContaDto: CreateContaDto, req: any): Promise<ContaResponseDto>;
    getDropdownOptions(req: any): Promise<ContaDropdownDto[]>;
    findAll(req: any, filterDto: FilterContasDto): Promise<ContaResponseDto[]>;
    findOne(id: string, req: any): Promise<ContaResponseDto>;
    update(id: string, updateContaDto: UpdateContaDto, req: any): Promise<ContaResponseDto>;
    remove(id: string, req: any): Promise<void>;
}
