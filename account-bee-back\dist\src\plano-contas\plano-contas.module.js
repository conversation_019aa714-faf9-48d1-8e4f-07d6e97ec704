"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PlanoContasModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const plano_conta_entity_1 = require("../entities/plano-conta.entity");
const plano_contas_controller_1 = require("./plano-contas.controller");
const plano_contas_service_1 = require("./plano-contas.service");
let PlanoContasModule = class PlanoContasModule {
};
exports.PlanoContasModule = PlanoContasModule;
exports.PlanoContasModule = PlanoContasModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([plano_conta_entity_1.PlanoConta])],
        controllers: [plano_contas_controller_1.PlanoContasController],
        providers: [plano_contas_service_1.PlanoContasService],
        exports: [plano_contas_service_1.PlanoContasService],
    })
], PlanoContasModule);
//# sourceMappingURL=plano-contas.module.js.map