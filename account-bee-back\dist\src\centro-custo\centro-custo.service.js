"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CentroCustoService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const uuid_1 = require("uuid");
const centro_custo_entity_1 = require("../entities/centro-custo.entity");
let CentroCustoService = class CentroCustoService {
    centroCustoRepository;
    constructor(centroCustoRepository) {
        this.centroCustoRepository = centroCustoRepository;
    }
    async create(createCentroCustoDto, empresaId, usuarioEmail) {
        console.log('🔍 Service - Criando centro de custo:', createCentroCustoDto);
        console.log('🔍 Service - EmpresaId:', empresaId);
        console.log('🔍 Service - UsuarioEmail:', usuarioEmail);
        try {
            const existingCentroCusto = await this.centroCustoRepository.findOne({
                where: {
                    eap: createCentroCustoDto.eap,
                    empresaId: empresaId,
                    isExcluido: 'N',
                },
            });
            if (existingCentroCusto) {
                throw new common_1.BadRequestException(`Já existe um centro de custo com o código EAP '${createCentroCustoDto.eap}' para esta empresa`);
            }
            const now = new Date();
            const centroCusto = this.centroCustoRepository.create({
                ...createCentroCustoDto,
                empresaId,
                uuid: (0, uuid_1.v4)(),
                dataHoraUsuarioInc: now,
                dataHoraUsuarioAlt: now,
                usuarioInc: usuarioEmail,
                usuarioAlt: usuarioEmail,
                isExcluido: 'N',
            });
            const savedCentroCusto = await this.centroCustoRepository.save(centroCusto);
            console.log('✅ Service - Centro de custo criado:', savedCentroCusto.id);
            return this.mapToResponseDto(savedCentroCusto);
        }
        catch (error) {
            console.error('❌ Service - Erro ao criar centro de custo:', error);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Erro interno do servidor ao criar centro de custo');
        }
    }
    async findAll(empresaId, filterDto = {}) {
        console.log('🔍 Service - Buscando centros de custo para empresa:', empresaId);
        console.log('🔍 Service - Filtros:', filterDto);
        console.log('🔍 Service - Repository:', !!this.centroCustoRepository);
        try {
            console.log('🔍 Service - Testando busca simples');
            const testQuery = await this.centroCustoRepository.findOne({
                where: { empresaId: empresaId }
            });
            console.log('🔍 Service - Resultado teste:', testQuery);
            const queryBuilder = this.centroCustoRepository.createQueryBuilder('cc');
            queryBuilder.where('cc.empresaId = :empresaId', { empresaId });
            const status = filterDto.status || 'ativo';
            if (status === 'ativo') {
                queryBuilder.andWhere('cc.isExcluido = :isExcluido', {
                    isExcluido: 'N',
                });
            }
            else if (status === 'excluido') {
                queryBuilder.andWhere('cc.isExcluido = :isExcluido', {
                    isExcluido: 'S',
                });
            }
            if (filterDto.descricao) {
                queryBuilder.andWhere('cc.descricao LIKE :descricao', {
                    descricao: `%${filterDto.descricao}%`,
                });
            }
            if (filterDto.eap) {
                queryBuilder.andWhere('cc.eap LIKE :eap', {
                    eap: `%${filterDto.eap}%`,
                });
            }
            if (filterDto.uuid) {
                queryBuilder.andWhere('cc.uuid = :uuid', { uuid: filterDto.uuid });
            }
            queryBuilder.orderBy('cc.dataHoraUsuarioInc', 'DESC');
            const centrosCusto = await queryBuilder.getMany();
            console.log('✅ Service - Encontrados', centrosCusto.length, 'centros de custo');
            return centrosCusto.map((cc) => this.mapToResponseDto(cc));
        }
        catch (error) {
            console.error('❌ Service - Erro ao buscar centros de custo:', error);
            throw new common_1.InternalServerErrorException('Erro interno do servidor ao buscar centros de custo');
        }
    }
    async findOne(id, empresaId) {
        console.log('🔍 Service - Buscando centro de custo:', id, 'da empresa:', empresaId);
        try {
            const centroCusto = await this.centroCustoRepository.findOne({
                where: {
                    id,
                    empresaId,
                    isExcluido: 'N',
                },
            });
            if (!centroCusto) {
                throw new common_1.NotFoundException('Centro de custo não encontrado');
            }
            console.log('✅ Service - Centro de custo encontrado:', centroCusto.id);
            return this.mapToResponseDto(centroCusto);
        }
        catch (error) {
            console.error('❌ Service - Erro ao buscar centro de custo:', error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Erro interno do servidor ao buscar centro de custo');
        }
    }
    async update(id, updateCentroCustoDto, empresaId, usuarioEmail) {
        console.log('🔍 Service - Atualizando centro de custo:', id);
        console.log('🔍 Service - Dados:', updateCentroCustoDto);
        try {
            const centroCusto = await this.centroCustoRepository.findOne({
                where: {
                    id,
                    empresaId,
                    isExcluido: 'N',
                },
            });
            if (!centroCusto) {
                throw new common_1.NotFoundException('Centro de custo não encontrado');
            }
            if (updateCentroCustoDto.eap &&
                updateCentroCustoDto.eap !== centroCusto.eap) {
                const existingCentroCusto = await this.centroCustoRepository
                    .createQueryBuilder('cc')
                    .where('cc.eap = :eap', { eap: updateCentroCustoDto.eap })
                    .andWhere('cc.empresaId = :empresaId', { empresaId })
                    .andWhere('cc.isExcluido = :isExcluido', { isExcluido: 'N' })
                    .andWhere('cc.id != :id', { id })
                    .getOne();
                if (existingCentroCusto) {
                    throw new common_1.BadRequestException(`Já existe um centro de custo com o código EAP '${updateCentroCustoDto.eap}' para esta empresa`);
                }
            }
            Object.assign(centroCusto, updateCentroCustoDto);
            centroCusto.dataHoraUsuarioAlt = new Date();
            centroCusto.usuarioAlt = usuarioEmail;
            const updatedCentroCusto = await this.centroCustoRepository.save(centroCusto);
            console.log('✅ Service - Centro de custo atualizado:', updatedCentroCusto.id);
            return this.mapToResponseDto(updatedCentroCusto);
        }
        catch (error) {
            console.error('❌ Service - Erro ao atualizar centro de custo:', error);
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Erro interno do servidor ao atualizar centro de custo');
        }
    }
    async remove(id, empresaId, usuarioEmail) {
        console.log('🔍 Service - Excluindo centro de custo:', id);
        try {
            const centroCusto = await this.centroCustoRepository.findOne({
                where: {
                    id,
                    empresaId,
                    isExcluido: 'N',
                },
            });
            if (!centroCusto) {
                throw new common_1.NotFoundException('Centro de custo não encontrado');
            }
            centroCusto.isExcluido = 'S';
            centroCusto.dataHoraUsuarioDel = new Date();
            centroCusto.usuarioDel = usuarioEmail;
            centroCusto.dataHoraUsuarioAlt = new Date();
            centroCusto.usuarioAlt = usuarioEmail;
            await this.centroCustoRepository.save(centroCusto);
            console.log('✅ Service - Centro de custo excluído logicamente:', id);
        }
        catch (error) {
            console.error('❌ Service - Erro ao excluir centro de custo:', error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Erro interno do servidor ao excluir centro de custo');
        }
    }
    mapToResponseDto(centroCusto) {
        return {
            id: centroCusto.id,
            descricao: centroCusto.descricao,
            eap: centroCusto.eap,
            empresaId: centroCusto.empresaId,
            dataHoraUsuarioAlt: centroCusto.dataHoraUsuarioAlt,
            dataHoraUsuarioDel: centroCusto.dataHoraUsuarioDel,
            dataHoraUsuarioInc: centroCusto.dataHoraUsuarioInc,
            dataSync: centroCusto.dataSync,
            isExcluido: centroCusto.isExcluido,
            usuarioAlt: centroCusto.usuarioAlt,
            usuarioDel: centroCusto.usuarioDel,
            usuarioInc: centroCusto.usuarioInc,
            uuid: centroCusto.uuid,
        };
    }
    async findAllForDropdown(empresaId) {
        const centrosCusto = await this.centroCustoRepository.find({
            where: {
                empresaId,
                isExcluido: 'N',
            },
            order: {
                descricao: 'ASC',
            },
        });
        return centrosCusto.map(centro => ({
            id: centro.id,
            descricao: centro.descricao,
        }));
    }
};
exports.CentroCustoService = CentroCustoService;
exports.CentroCustoService = CentroCustoService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(centro_custo_entity_1.CentroCusto)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], CentroCustoService);
//# sourceMappingURL=centro-custo.service.js.map