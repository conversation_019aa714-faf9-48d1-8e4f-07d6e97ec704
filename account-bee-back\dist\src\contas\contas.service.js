"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContasService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const uuid_1 = require("uuid");
const conta_entity_1 = require("../entities/conta.entity");
let ContasService = class ContasService {
    contaRepository;
    constructor(contaRepository) {
        this.contaRepository = contaRepository;
    }
    async create(createContaDto, empresaId, usuarioEmail) {
        try {
            const agora = new Date();
            const conta = new conta_entity_1.Conta();
            conta.descricao = createContaDto.banco;
            conta.empresaId = empresaId;
            conta.isExcluido = createContaDto.contaAtiva !== false ? 'N' : 'S';
            conta.usuarioInc = usuarioEmail;
            conta.usuarioAlt = usuarioEmail;
            conta.dataHoraUsuarioInc = agora;
            conta.dataHoraUsuarioAlt = agora;
            conta.uuid = (0, uuid_1.v4)();
            if (createContaDto.saldoInicial !== undefined &&
                createContaDto.saldoInicial !== null) {
                conta.saldoInicial = createContaDto.saldoInicial;
            }
            if (createContaDto.dataSaldoInicial) {
                conta.dataSaldoInicial = new Date(createContaDto.dataSaldoInicial);
            }
            console.log('🔍 Service - Conta a ser salva:', conta);
            const savedConta = await this.contaRepository.save(conta);
            return this.mapToResponseDto(savedConta);
        }
        catch (error) {
            console.error('Erro ao criar conta:', error);
            throw new common_1.BadRequestException(`Erro ao criar conta bancária: ${error.message}`);
        }
    }
    async findAll(empresaId, filterDto) {
        const queryBuilder = this.contaRepository.createQueryBuilder('conta');
        queryBuilder.where('conta.empresaId = :empresaId', { empresaId });
        if (filterDto?.contaAtiva !== false) {
            queryBuilder.andWhere('conta.isExcluido = :isExcluido', {
                isExcluido: 'N',
            });
        }
        else {
            queryBuilder.andWhere('conta.isExcluido = :isExcluido', {
                isExcluido: 'S',
            });
        }
        if (filterDto?.banco) {
            queryBuilder.andWhere('conta.descricao LIKE :banco', {
                banco: `%${filterDto.banco}%`,
            });
        }
        queryBuilder.orderBy('conta.descricao', 'ASC');
        const contas = await queryBuilder.getMany();
        return contas.map((conta) => this.mapToResponseDto(conta));
    }
    async findOne(id, empresaId) {
        const conta = await this.contaRepository.findOne({
            where: {
                id,
                empresaId,
                isExcluido: 'N',
            },
        });
        if (!conta) {
            throw new common_1.NotFoundException(`Conta com ID ${id} não encontrada`);
        }
        return this.mapToResponseDto(conta);
    }
    async update(id, updateContaDto, empresaId, usuarioEmail) {
        const conta = await this.contaRepository.findOne({
            where: {
                id,
                empresaId,
                isExcluido: 'N',
            },
        });
        if (!conta) {
            throw new common_1.NotFoundException(`Conta com ID ${id} não encontrada`);
        }
        const agora = new Date();
        if (updateContaDto.banco !== undefined) {
            conta.descricao = updateContaDto.banco;
        }
        if (updateContaDto.saldoInicial !== undefined) {
            conta.saldoInicial = updateContaDto.saldoInicial;
        }
        if (updateContaDto.dataSaldoInicial !== undefined) {
            conta.dataSaldoInicial = updateContaDto.dataSaldoInicial
                ? new Date(updateContaDto.dataSaldoInicial)
                : undefined;
        }
        if (updateContaDto.contaAtiva !== undefined) {
            conta.isExcluido = updateContaDto.contaAtiva ? 'N' : 'S';
        }
        conta.usuarioAlt = usuarioEmail;
        conta.dataHoraUsuarioAlt = agora;
        const updatedConta = await this.contaRepository.save(conta);
        return this.mapToResponseDto(updatedConta);
    }
    async remove(id, empresaId, usuarioEmail) {
        const conta = await this.contaRepository.findOne({
            where: {
                id,
                empresaId,
                isExcluido: 'N',
            },
        });
        if (!conta) {
            throw new common_1.NotFoundException(`Conta com ID ${id} não encontrada`);
        }
        const agora = new Date();
        conta.isExcluido = 'S';
        conta.usuarioDel = usuarioEmail;
        conta.dataHoraUsuarioDel = agora;
        await this.contaRepository.save(conta);
    }
    mapToResponseDto(conta) {
        return {
            id: conta.id,
            banco: conta.descricao,
            saldoInicial: conta.saldoInicial,
            dataSaldoInicial: conta.dataSaldoInicial,
            contaAtiva: conta.isExcluido === 'N',
            empresaId: conta.empresaId,
            dataHoraUsuarioInc: conta.dataHoraUsuarioInc,
            dataHoraUsuarioAlt: conta.dataHoraUsuarioAlt,
            usuarioInc: conta.usuarioInc,
            usuarioAlt: conta.usuarioAlt,
        };
    }
    async findAllForDropdown(empresaId) {
        const contas = await this.contaRepository.find({
            where: {
                empresaId,
                isExcluido: 'N',
            },
            order: {
                descricao: 'ASC',
            },
        });
        return contas.map(conta => ({
            id: conta.id,
            descricao: conta.descricao,
        }));
    }
};
exports.ContasService = ContasService;
exports.ContasService = ContasService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(conta_entity_1.Conta)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ContasService);
//# sourceMappingURL=contas.service.js.map