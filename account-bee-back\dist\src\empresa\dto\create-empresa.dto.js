"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateEmpresaDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateEmpresaDto {
    nomeEmpresa;
    razaoSocial;
    ddd;
    telefone;
    horaFimExpediente;
}
exports.CreateEmpresaDto = CreateEmpresaDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome da empresa ou razão social',
        example: 'Empresa Exemplo LTDA',
        minLength: 2,
        maxLength: 255,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Nome da empresa é obrigatório' }),
    (0, class_validator_1.IsString)({ message: 'Nome da empresa deve ser uma string' }),
    (0, class_validator_1.MinLength)(2, { message: 'Nome da empresa deve ter no mínimo 2 caracteres' }),
    (0, class_validator_1.MaxLength)(255, {
        message: 'Nome da empresa deve ter no máximo 255 caracteres',
    }),
    __metadata("design:type", String)
], CreateEmpresaDto.prototype, "nomeEmpresa", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Razão social da empresa',
        example: 'Empresa Exemplo LTDA',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Razão social deve ser uma string' }),
    (0, class_validator_1.MaxLength)(255, { message: 'Razão social deve ter no máximo 255 caracteres' }),
    __metadata("design:type", String)
], CreateEmpresaDto.prototype, "razaoSocial", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'DDD da empresa',
        example: '11',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'DDD deve ser uma string' }),
    (0, class_validator_1.MaxLength)(100, { message: 'DDD deve ter no máximo 100 caracteres' }),
    __metadata("design:type", String)
], CreateEmpresaDto.prototype, "ddd", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Telefone da empresa',
        example: '(11) 3333-4444',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Telefone deve ser uma string' }),
    (0, class_validator_1.Matches)(/^\(\d{2}\)\s?\d{4,5}-?\d{4}$/, {
        message: 'Telefone deve estar no formato (XX) XXXXX-XXXX ou (XX) XXXX-XXXX',
    }),
    __metadata("design:type", String)
], CreateEmpresaDto.prototype, "telefone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Hora de fim do expediente',
        example: '23:59',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Hora de fim do expediente deve ser uma string' }),
    (0, class_validator_1.MaxLength)(5, {
        message: 'Hora de fim do expediente deve ter no máximo 5 caracteres',
    }),
    __metadata("design:type", String)
], CreateEmpresaDto.prototype, "horaFimExpediente", void 0);
//# sourceMappingURL=create-empresa.dto.js.map