"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Pessoa = void 0;
const typeorm_1 = require("typeorm");
const empresa_entity_1 = require("./empresa.entity");
let Pessoa = class Pessoa {
    id;
    dataHoraUsuarioAlt;
    dataHoraUsuarioDel;
    dataHoraUsuarioInc;
    dataSync;
    isExcluido;
    usuarioAlt;
    usuarioDel;
    usuarioInc;
    uuid;
    nome;
    dataNascimento;
    email;
    cpf;
    cnpj;
    empresaId;
    empresa;
};
exports.Pessoa = Pessoa;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'ID' }),
    __metadata("design:type", Number)
], Pessoa.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_HORA_USUARIO_ALT', type: 'datetime' }),
    __metadata("design:type", Date)
], Pessoa.prototype, "dataHoraUsuarioAlt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_HORA_USUARIO_DEL', type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], Pessoa.prototype, "dataHoraUsuarioDel", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_HORA_USUARIO_INC', type: 'datetime' }),
    __metadata("design:type", Date)
], Pessoa.prototype, "dataHoraUsuarioInc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_SYNC', type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], Pessoa.prototype, "dataSync", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_EXCLUIDO', length: 1, default: 'N' }),
    __metadata("design:type", String)
], Pessoa.prototype, "isExcluido", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_ALT', length: 500 }),
    __metadata("design:type", String)
], Pessoa.prototype, "usuarioAlt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_DEL', length: 500, nullable: true }),
    __metadata("design:type", String)
], Pessoa.prototype, "usuarioDel", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_INC', length: 500 }),
    __metadata("design:type", String)
], Pessoa.prototype, "usuarioInc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'UUID', length: 50, nullable: true }),
    __metadata("design:type", String)
], Pessoa.prototype, "uuid", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'NOME', length: 100 }),
    __metadata("design:type", String)
], Pessoa.prototype, "nome", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_NASCIMENTO', type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Pessoa.prototype, "dataNascimento", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'EMAIL', length: 100, nullable: true }),
    __metadata("design:type", String)
], Pessoa.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CPF', length: 50, nullable: true }),
    __metadata("design:type", String)
], Pessoa.prototype, "cpf", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CNPJ', length: 50, nullable: true }),
    __metadata("design:type", String)
], Pessoa.prototype, "cnpj", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'EMPRESA_ID' }),
    __metadata("design:type", Number)
], Pessoa.prototype, "empresaId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => empresa_entity_1.Empresa),
    (0, typeorm_1.JoinColumn)({ name: 'EMPRESA_ID' }),
    __metadata("design:type", empresa_entity_1.Empresa)
], Pessoa.prototype, "empresa", void 0);
exports.Pessoa = Pessoa = __decorate([
    (0, typeorm_1.Entity)({ name: 'pessoa' })
], Pessoa);
//# sourceMappingURL=pessoa.entity.js.map