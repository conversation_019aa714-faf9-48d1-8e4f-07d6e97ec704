"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FornecedorService = void 0;
const common_1 = require("@nestjs/common");
const uuid_1 = require("uuid");
const fornecedor_repository_1 = require("./fornecedor.repository");
const fornecedor_validation_1 = require("./fornecedor.validation");
let FornecedorService = class FornecedorService {
    fornecedorRepository;
    fornecedorValidation;
    constructor(fornecedorRepository, fornecedorValidation) {
        this.fornecedorRepository = fornecedorRepository;
        this.fornecedorValidation = fornecedorValidation;
    }
    async create(createFornecedorDto, user) {
        console.log('🔍 Service - Criando fornecedor:', createFornecedorDto);
        console.log('🔍 Service - Usuário:', user?.email);
        try {
            const empresaId = user.empresaId;
            await this.fornecedorValidation.validateCreate(createFornecedorDto, empresaId);
            const now = new Date();
            const { endereco, contatos, pessoas, ...fornecedorProperties } = createFornecedorDto;
            const fornecedorData = {
                ...fornecedorProperties,
                empresaId,
                uuid: (0, uuid_1.v4)(),
                dataHoraUsuarioInc: now,
                dataHoraUsuarioAlt: now,
                usuarioInc: user.email,
                usuarioAlt: user.email,
                isExcluido: 'N',
            };
            const savedFornecedor = await this.fornecedorRepository.create(fornecedorData);
            if (endereco) {
                console.log('📍 Endereco data received:', {
                    fornecedorId: savedFornecedor.id,
                    endereco: endereco,
                    timestamp: new Date().toISOString()
                });
            }
            if (contatos && contatos.length > 0) {
                console.log('📞 Contatos data received:', {
                    fornecedorId: savedFornecedor.id,
                    contatos: contatos.map((contato, index) => ({
                        index,
                        nome: contato.nome,
                        cargo: contato.cargo,
                        telefone: contato.telefone,
                        email: contato.email,
                        observacao: contato.observacao
                    })),
                    timestamp: new Date().toISOString()
                });
            }
            if (pessoas && pessoas.length > 0) {
                console.log('👥 Pessoas data received:', {
                    fornecedorId: savedFornecedor.id,
                    pessoas: pessoas.map((pessoa, index) => ({
                        index,
                        nome: pessoa.nome,
                        email: pessoa.email,
                        cpf: pessoa.cpf,
                        telefone: pessoa.telefone,
                        empresaId: pessoa.empresaId
                    })),
                    timestamp: new Date().toISOString()
                });
            }
            console.log('✅ Service - Fornecedor criado:', savedFornecedor.id);
            return this.mapToResponseDto(savedFornecedor);
        }
        catch (error) {
            console.error('❌ Service - Erro ao criar fornecedor:', error);
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Erro interno do servidor ao criar fornecedor');
        }
    }
    async findAll(user, filterDto = {}) {
        console.log('🔍 Service - Buscando fornecedores');
        console.log('🔍 Service - Usuário:', user?.email);
        console.log('🔍 Service - Filtros:', filterDto);
        try {
            const empresaId = user.empresaId;
            const page = filterDto.page || 1;
            const limit = Math.min(filterDto.limit || 10, 100);
            const paginatedFilter = {
                ...filterDto,
                page,
                limit,
            };
            const { data, total } = await this.fornecedorRepository.findAll(empresaId, paginatedFilter);
            console.log('✅ Service - Encontrados', total, 'fornecedores');
            return {
                data: data.map((fornecedor) => this.mapToResponseDto(fornecedor)),
                total,
                page,
                limit,
                totalPages: Math.ceil(total / limit),
            };
        }
        catch (error) {
            console.error('❌ Service - Erro ao buscar fornecedores:', error);
            throw new common_1.InternalServerErrorException('Erro interno do servidor ao buscar fornecedores');
        }
    }
    async findOne(id, user) {
        console.log('🔍 Service - Buscando fornecedor:', id, 'tipo:', typeof id);
        console.log('🔍 Service - Usuário:', user?.email);
        console.log('🔍 Service - Stack trace:', new Error().stack);
        try {
            const empresaId = user.empresaId;
            const fornecedor = await this.fornecedorRepository.findById(id, empresaId);
            if (!fornecedor) {
                throw new common_1.NotFoundException('Fornecedor não encontrado');
            }
            console.log('✅ Service - Fornecedor encontrado:', fornecedor.id);
            return this.mapToResponseDto(fornecedor);
        }
        catch (error) {
            console.error('❌ Service - Erro ao buscar fornecedor:', error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Erro interno do servidor ao buscar fornecedor');
        }
    }
    async update(id, updateFornecedorDto, user) {
        console.log('🔍 Service - Atualizando fornecedor:', id);
        console.log('🔍 Service - Dados:', updateFornecedorDto);
        try {
            const empresaId = user.empresaId;
            const existingFornecedor = await this.fornecedorRepository.findById(id, empresaId);
            if (!existingFornecedor) {
                throw new common_1.NotFoundException('Fornecedor não encontrado');
            }
            await this.fornecedorValidation.validateUpdate(id, updateFornecedorDto, empresaId);
            const { endereco, contatos, pessoas, ...fornecedorProperties } = updateFornecedorDto;
            const updateData = {
                ...fornecedorProperties,
                dataHoraUsuarioAlt: new Date(),
                usuarioAlt: user.email,
            };
            const updatedFornecedor = await this.fornecedorRepository.update(id, updateData);
            if (endereco) {
                console.log('📍 Endereco data received for update:', {
                    fornecedorId: id,
                    endereco: endereco,
                    timestamp: new Date().toISOString()
                });
            }
            if (contatos && contatos.length > 0) {
                console.log('📞 Contatos data received for update:', {
                    fornecedorId: id,
                    contatos: contatos.map((contato, index) => ({
                        index,
                        nome: contato.nome,
                        cargo: contato.cargo,
                        telefone: contato.telefone,
                        email: contato.email,
                        observacao: contato.observacao
                    })),
                    timestamp: new Date().toISOString()
                });
            }
            if (pessoas && pessoas.length > 0) {
                console.log('👥 Pessoas data received for update:', {
                    fornecedorId: id,
                    pessoas: pessoas.map((pessoa, index) => ({
                        index,
                        nome: pessoa.nome,
                        email: pessoa.email,
                        cpf: pessoa.cpf,
                        telefone: pessoa.telefone,
                        empresaId: pessoa.empresaId
                    })),
                    timestamp: new Date().toISOString()
                });
            }
            console.log('✅ Service - Fornecedor atualizado:', updatedFornecedor.id);
            return this.mapToResponseDto(updatedFornecedor);
        }
        catch (error) {
            console.error('❌ Service - Erro ao atualizar fornecedor:', error);
            if (error instanceof common_1.NotFoundException ||
                error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Erro interno do servidor ao atualizar fornecedor');
        }
    }
    async remove(id, user) {
        console.log('🔍 Service - Excluindo fornecedor:', id);
        try {
            const empresaId = user.empresaId;
            const existingFornecedor = await this.fornecedorRepository.findById(id, empresaId);
            if (!existingFornecedor) {
                throw new common_1.NotFoundException('Fornecedor não encontrado');
            }
            await this.fornecedorValidation.validateDelete(id, empresaId);
            await this.fornecedorRepository.delete(id, user.email);
            console.log('✅ Service - Fornecedor excluído logicamente:', id);
        }
        catch (error) {
            console.error('❌ Service - Erro ao excluir fornecedor:', error);
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.InternalServerErrorException('Erro interno do servidor ao excluir fornecedor');
        }
    }
    mapToResponseDto(fornecedor) {
        return {
            id: fornecedor.id,
            uuid: fornecedor.uuid,
            descricao: fornecedor.descricao,
            cpf: fornecedor.cpf,
            cnpj: fornecedor.cnpj,
            inscricaoEstadual: fornecedor.inscricaoEstadual,
            inscricaoMunicipal: fornecedor.inscricaoMunicipal,
            prazoEntrega: fornecedor.prazoEntrega,
            prazoPagamento: fornecedor.prazoPagamento,
            formaPagamento: fornecedor.formaPagamento,
            empresaId: fornecedor.empresaId,
            enderecoId: fornecedor.enderecoId,
            planoContaId: fornecedor.planoContaId,
            isExcluido: fornecedor.isExcluido,
            dataHoraUsuarioInc: fornecedor.dataHoraUsuarioInc,
            dataHoraUsuarioAlt: fornecedor.dataHoraUsuarioAlt,
            dataHoraUsuarioDel: fornecedor.dataHoraUsuarioDel,
            usuarioInc: fornecedor.usuarioInc,
            usuarioAlt: fornecedor.usuarioAlt,
            usuarioDel: fornecedor.usuarioDel,
            dataSync: fornecedor.dataSync,
        };
    }
    async findAllForDropdown(empresaId) {
        const fornecedores = await this.fornecedorRepository.findAllByEmpresa(empresaId);
        return fornecedores.map(fornecedor => ({
            id: fornecedor.id,
            descricao: fornecedor.descricao,
        }));
    }
};
exports.FornecedorService = FornecedorService;
exports.FornecedorService = FornecedorService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [fornecedor_repository_1.FornecedorRepository,
        fornecedor_validation_1.FornecedorValidation])
], FornecedorService);
//# sourceMappingURL=fornecedor.service.js.map