"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Conta = void 0;
const typeorm_1 = require("typeorm");
const empresa_entity_1 = require("./empresa.entity");
let Conta = class Conta {
    id;
    descricao;
    empresa;
    empresaId;
    saldoInicial;
    dataSaldoInicial;
    planoContaId;
    isExcluido;
    usuarioInc;
    usuarioAlt;
    usuarioDel;
    dataHoraUsuarioInc;
    dataHoraUsuarioAlt;
    dataHoraUsuarioDel;
    uuid;
    dataSync;
};
exports.Conta = Conta;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'ID' }),
    __metadata("design:type", Number)
], Conta.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DESCRICAO', length: 200, nullable: false }),
    __metadata("design:type", String)
], Conta.prototype, "descricao", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => empresa_entity_1.Empresa),
    (0, typeorm_1.JoinColumn)({ name: 'EMPRESA_ID' }),
    __metadata("design:type", empresa_entity_1.Empresa)
], Conta.prototype, "empresa", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'EMPRESA_ID', nullable: false }),
    __metadata("design:type", Number)
], Conta.prototype, "empresaId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'SALDO_INICIAL', type: 'double', nullable: true }),
    __metadata("design:type", Number)
], Conta.prototype, "saldoInicial", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'DATA_SALDO_INICIAL',
        type: 'timestamp',
        precision: 6,
        nullable: true,
    }),
    __metadata("design:type", Date)
], Conta.prototype, "dataSaldoInicial", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'PLANO_CONTA_ID', nullable: true }),
    __metadata("design:type", Number)
], Conta.prototype, "planoContaId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_EXCLUIDO', length: 1, nullable: false }),
    __metadata("design:type", String)
], Conta.prototype, "isExcluido", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_INC', length: 500, nullable: false }),
    __metadata("design:type", String)
], Conta.prototype, "usuarioInc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_ALT', length: 500, nullable: false }),
    __metadata("design:type", String)
], Conta.prototype, "usuarioAlt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_DEL', length: 500, nullable: true }),
    __metadata("design:type", String)
], Conta.prototype, "usuarioDel", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'DATA_HORA_USUARIO_INC',
        type: 'timestamp',
        precision: 6,
        nullable: false,
    }),
    __metadata("design:type", Date)
], Conta.prototype, "dataHoraUsuarioInc", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'DATA_HORA_USUARIO_ALT',
        type: 'timestamp',
        precision: 6,
        nullable: false,
    }),
    __metadata("design:type", Date)
], Conta.prototype, "dataHoraUsuarioAlt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'DATA_HORA_USUARIO_DEL',
        type: 'timestamp',
        precision: 6,
        nullable: true,
    }),
    __metadata("design:type", Date)
], Conta.prototype, "dataHoraUsuarioDel", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'UUID', length: 50, nullable: true }),
    __metadata("design:type", String)
], Conta.prototype, "uuid", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'DATA_SYNC',
        type: 'timestamp',
        precision: 6,
        nullable: true,
    }),
    __metadata("design:type", Date)
], Conta.prototype, "dataSync", void 0);
exports.Conta = Conta = __decorate([
    (0, typeorm_1.Entity)({ name: 'conta' })
], Conta);
//# sourceMappingURL=conta.entity.js.map