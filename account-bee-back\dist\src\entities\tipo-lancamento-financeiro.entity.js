"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TipoLancamentoFinanceiro = void 0;
const typeorm_1 = require("typeorm");
let TipoLancamentoFinanceiro = class TipoLancamentoFinanceiro {
    id;
    descricao;
    empresaId;
    dataHoraUsuarioAlt;
    dataHoraUsuarioDel;
    dataHoraUsuarioInc;
    dataSync;
    isExcluido;
    usuarioAlt;
    usuarioDel;
    usuarioInc;
    uuid;
};
exports.TipoLancamentoFinanceiro = TipoLancamentoFinanceiro;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'ID' }),
    __metadata("design:type", Number)
], TipoLancamentoFinanceiro.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DESCRICAO', length: 200, nullable: false }),
    __metadata("design:type", String)
], TipoLancamentoFinanceiro.prototype, "descricao", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'EMPRESA_ID', nullable: false }),
    __metadata("design:type", Number)
], TipoLancamentoFinanceiro.prototype, "empresaId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'DATA_HORA_USUARIO_ALT',
        type: 'datetime',
        precision: 6,
        nullable: false,
    }),
    __metadata("design:type", Date)
], TipoLancamentoFinanceiro.prototype, "dataHoraUsuarioAlt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'DATA_HORA_USUARIO_DEL',
        type: 'datetime',
        precision: 6,
        nullable: true,
    }),
    __metadata("design:type", Date)
], TipoLancamentoFinanceiro.prototype, "dataHoraUsuarioDel", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'DATA_HORA_USUARIO_INC',
        type: 'datetime',
        precision: 6,
        nullable: false,
    }),
    __metadata("design:type", Date)
], TipoLancamentoFinanceiro.prototype, "dataHoraUsuarioInc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_SYNC', type: 'datetime', precision: 6, nullable: true }),
    __metadata("design:type", Date)
], TipoLancamentoFinanceiro.prototype, "dataSync", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_EXCLUIDO', length: 1, nullable: false, default: 'N' }),
    __metadata("design:type", String)
], TipoLancamentoFinanceiro.prototype, "isExcluido", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_ALT', length: 500, nullable: false }),
    __metadata("design:type", String)
], TipoLancamentoFinanceiro.prototype, "usuarioAlt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_DEL', length: 500, nullable: true }),
    __metadata("design:type", String)
], TipoLancamentoFinanceiro.prototype, "usuarioDel", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_INC', length: 500, nullable: false }),
    __metadata("design:type", String)
], TipoLancamentoFinanceiro.prototype, "usuarioInc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'UUID', length: 50, nullable: true }),
    __metadata("design:type", String)
], TipoLancamentoFinanceiro.prototype, "uuid", void 0);
exports.TipoLancamentoFinanceiro = TipoLancamentoFinanceiro = __decorate([
    (0, typeorm_1.Entity)({ name: 'TIPO_LANCAMENTO_FINANCEIRO' })
], TipoLancamentoFinanceiro);
//# sourceMappingURL=tipo-lancamento-financeiro.entity.js.map