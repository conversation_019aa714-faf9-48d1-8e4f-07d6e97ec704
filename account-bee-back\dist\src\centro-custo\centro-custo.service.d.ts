import { Repository } from 'typeorm';
import { CentroCusto } from '../entities/centro-custo.entity';
import { CreateCentroCustoDto } from './dto/create-centro-custo.dto';
import { UpdateCentroCustoDto } from './dto/update-centro-custo.dto';
import { FilterCentroCustoDto } from './dto/filter-centro-custo.dto';
import { CentroCustoResponseDto } from './dto/centro-custo-response.dto';
import { CentroCustoDropdownDto } from '../lancamentos-financeiros/dto';
export declare class CentroCustoService {
    private centroCustoRepository;
    constructor(centroCustoRepository: Repository<CentroCusto>);
    create(createCentroCustoDto: CreateCentroCustoDto, empresaId: number, usuarioEmail: string): Promise<CentroCustoResponseDto>;
    findAll(empresaId: number, filterDto?: FilterCentroCustoDto): Promise<CentroCustoResponseDto[]>;
    findOne(id: number, empresaId: number): Promise<CentroCustoResponseDto>;
    update(id: number, updateCentroCustoDto: UpdateCentroCustoDto, empresaId: number, usuarioEmail: string): Promise<CentroCustoResponseDto>;
    remove(id: number, empresaId: number, usuarioEmail: string): Promise<void>;
    private mapToResponseDto;
    findAllForDropdown(empresaId: number): Promise<CentroCustoDropdownDto[]>;
}
