export declare class CostCenterAllocationDto {
    centroCustoId: number;
    valor: number;
    porcentagem?: number;
}
export declare class CreateLancamentoFinanceiroDto {
    pessoaId: number;
    descricao: string;
    valor: number;
    dataLancamento?: string;
    dataCompetencia?: string;
    contaId?: number;
    localId?: number;
    valorBruto?: number;
    categoriaLctoFinanceiroId?: number;
    alocacoesCentroCusto?: CostCenterAllocationDto[];
    planoContaCredito?: number;
    fornecedorId?: number;
    observacao?: string;
    repetirReceita?: boolean;
    tipoRepeticao?: 'este' | 'esteEProximos';
    periodicidade?: 'mensal' | 'semanal' | 'anual';
    quantidadeRepeticoes?: number;
    empresaId?: number;
    tipoLancamentoFinanceiroId?: number;
}
