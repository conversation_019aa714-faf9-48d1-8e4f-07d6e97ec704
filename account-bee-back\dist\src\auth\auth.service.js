"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const jwt_1 = require("@nestjs/jwt");
const usuario_entity_1 = require("../entities/usuario.entity");
const empresa_entity_1 = require("../entities/empresa.entity");
const legacy_crypto_service_1 = require("../crypto/legacy-crypto.service");
const email_service_1 = require("../email/email.service");
const empresa_service_1 = require("../empresa/empresa.service");
const crypto = __importStar(require("crypto"));
let AuthService = class AuthService {
    usuarioRepository;
    empresaRepository;
    jwtService;
    cryptoService;
    emailService;
    empresaService;
    constructor(usuarioRepository, empresaRepository, jwtService, cryptoService, emailService, empresaService) {
        this.usuarioRepository = usuarioRepository;
        this.empresaRepository = empresaRepository;
        this.jwtService = jwtService;
        this.cryptoService = cryptoService;
        this.emailService = emailService;
        this.empresaService = empresaService;
    }
    async login(loginDto) {
        try {
            let email = loginDto.email;
            let senha = loginDto.senha;
            if (!loginDto.senhaCriptografada) {
                email = this.cryptoService.encrypt(loginDto.email, 0) || '';
                senha = this.cryptoService.encrypt(loginDto.senha, 0) || '';
            }
            console.log('Login attempt:', {
                originalEmail: loginDto.email,
                encryptedEmail: email,
                encryptedPassword: senha,
                isEncrypted: loginDto.senhaCriptografada,
            });
            const usuario = await this.usuarioRepository.findOne({
                where: {
                    email: email,
                    senha: senha,
                    isExcluido: 'N',
                },
            });
            if (!usuario) {
                const userByEmail = await this.usuarioRepository.findOne({
                    where: {
                        email: email,
                        isExcluido: 'N',
                    },
                });
                if (userByEmail) {
                    console.log('Usuário encontrado, mas senha não confere:', {
                        emailBanco: userByEmail.email,
                        senhaBanco: userByEmail.senha,
                        senhaEnviada: senha,
                    });
                }
                else {
                    console.log('Usuário não encontrado com email:', email);
                }
                return {
                    erro: 'Usuário ou senha inválidos',
                    mensagem: 'Usuário ou senha inválidos',
                    tipo: 'danger',
                };
            }
            if (!usuario.aceitaTermoUso) {
                return {
                    erro: 'É necessário aceitar os termos de uso',
                    mensagem: 'É necessário aceitar os termos de uso',
                    tipo: 'danger',
                };
            }
            if (usuario.twoAuthentication) {
                const code = this.generateVerificationCode();
                await this.saveVerificationCode(usuario, code);
                const emailDecrypt = this.cryptoService.decrypt(usuario.email || '', 0) || loginDto.email;
                await this.emailService.sendVerificationCode(emailDecrypt, code);
                return {
                    mensagem: 'Insira o código de verificação enviado para seu email',
                    tipo: 'info',
                    codigo: 'INSIRA_COD_VERIFICACAO',
                };
            }
            return await this.authenticateUser(usuario);
        }
        catch (error) {
            console.error('Erro no login:', error);
            return {
                erro: 'Erro ao realizar login',
                mensagem: 'Erro ao realizar login',
                tipo: 'danger',
            };
        }
    }
    async verifyCode(verifyCodeDto) {
        try {
            let email = verifyCodeDto.email;
            let senha = verifyCodeDto.senha;
            if (!verifyCodeDto.senhaCriptografada) {
                email = this.cryptoService.encrypt(verifyCodeDto.email, 0) || '';
                senha = this.cryptoService.encrypt(verifyCodeDto.senha, 0) || '';
            }
            const usuario = await this.usuarioRepository.findOne({
                where: {
                    email: email,
                    senha: senha,
                    isExcluido: 'N',
                },
            });
            if (!usuario) {
                return {
                    erro: 'Usuário ou senha inválidos',
                    mensagem: 'Usuário ou senha inválidos',
                    tipo: 'danger',
                };
            }
            let storedCode = usuario.codeVerificacao;
            if (storedCode && !usuario.acessaMenuVendas) {
                storedCode = this.cryptoService.decrypt(storedCode, 0) || '';
            }
            const codeEnviado = verifyCodeDto.codeVerificacao.toUpperCase();
            console.log('Verificando código:', {
                codeEnviado,
                storedCode,
                match: codeEnviado === storedCode,
            });
            if (codeEnviado !== storedCode) {
                return {
                    erro: 'Código inválido',
                    mensagem: 'Código inválido',
                    tipo: 'danger',
                };
            }
            await this.saveVerificationCode(usuario, null);
            return await this.authenticateUser(usuario);
        }
        catch (error) {
            console.error('Erro ao verificar código:', error);
            return {
                erro: 'Erro ao verificar código',
                mensagem: 'Erro ao verificar código',
                tipo: 'danger',
            };
        }
    }
    async authenticateUser(usuario) {
        let token = usuario.token;
        let needUpdate = false;
        if (!token ||
            !usuario.dataExpiracaoToken ||
            new Date() >= usuario.dataExpiracaoToken) {
            token = this.generateToken();
            needUpdate = true;
        }
        if (needUpdate) {
            usuario.token = token;
            usuario.dataExpiracaoToken = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
            usuario.dataHoraUsuarioAlt = new Date();
            usuario.usuarioAlt = 'API';
            await this.usuarioRepository.save(usuario);
        }
        if (usuario.codeVerificacao) {
            usuario.codeVerificacao = undefined;
            await this.usuarioRepository.save(usuario);
        }
        let decryptedEmail;
        if (usuario.email) {
            decryptedEmail =
                this.cryptoService.decrypt(usuario.email, 0) || undefined;
            if (!decryptedEmail) {
                decryptedEmail =
                    this.cryptoService.decrypt(usuario.email, usuario.empresaId || 0) ||
                        undefined;
            }
        }
        const usuarioResponse = {
            id: usuario.id,
            email: decryptedEmail || usuario.email,
            nomeUsuario: usuario.nomeUsuario,
            token: token,
            empresaId: usuario.empresaId,
            admGeral: usuario.admGeral,
            admLocal: usuario.admLocal,
            operador: usuario.operador,
            operadorEstoque: usuario.operadorEstoque,
            visualizaSaldosBancarios: usuario.visualizaSaldosBancarios,
            gerenciarLctosFinanceiros: usuario.gerenciarLctosFinanceiros,
            relatoriosFinanceiros: usuario.relatoriosFinanceiros,
            gerenciarProdutos: usuario.gerenciarProdutos,
            podeAcessarFiscal: usuario.podeAcessarFiscal,
            podeAcessarConfiguracoes: usuario.podeAcessarConfiguracoes,
            twoAuthentication: usuario.twoAuthentication,
        };
        const jwtPayload = {
            sub: usuario.id,
            email: decryptedEmail || usuario.email,
            token: token,
            empresaId: usuario.empresaId,
        };
        const jwtToken = await this.jwtService.signAsync(jwtPayload);
        return {
            usuarioVo: usuarioResponse,
            mensagem: 'Login realizado com sucesso',
            tipo: 'success',
            codigo: jwtToken,
        };
    }
    generateToken() {
        return crypto.randomBytes(32).toString('hex');
    }
    generateVerificationCode() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let code = '';
        for (let i = 0; i < 5; i++) {
            code += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return code;
    }
    async saveVerificationCode(usuario, code) {
        if (code && !usuario.acessaMenuVendas) {
            const encrypted = this.cryptoService.encrypt(code, 0);
            usuario.codeVerificacao = encrypted || undefined;
        }
        else {
            usuario.codeVerificacao = code || undefined;
        }
        usuario.dataHoraUsuarioAlt = new Date();
        usuario.usuarioAlt = 'API';
        await this.usuarioRepository.save(usuario);
    }
    async validateToken(token) {
        const usuario = await this.usuarioRepository.findOne({
            where: {
                token: token,
                isExcluido: 'N',
            },
        });
        if (usuario &&
            usuario.dataExpiracaoToken &&
            new Date() > usuario.dataExpiracaoToken) {
            return null;
        }
        return usuario;
    }
    async register(registrationDto) {
        try {
            const encryptedEmail = this.cryptoService.encrypt(registrationDto.email, 0);
            if (!encryptedEmail) {
                throw new common_1.BadRequestException('Erro ao criptografar email');
            }
            const existingUser = await this.usuarioRepository.findOne({
                where: { email: encryptedEmail, isExcluido: 'N' },
            });
            if (existingUser) {
                return {
                    erro: 'Email já cadastrado',
                    mensagem: 'Este email já está em uso',
                    tipo: 'danger',
                };
            }
            const empresa = await this.empresaService.create({
                nomeEmpresa: registrationDto.empresaRazaoSocial,
                razaoSocial: registrationDto.empresaRazaoSocial,
                ddd: registrationDto.empresaTelefone
                    ? registrationDto.empresaTelefone
                        .replace(/[^\d]/g, '')
                        .substring(0, 2)
                    : undefined,
                telefone: registrationDto.empresaTelefone,
                horaFimExpediente: '23:59',
            });
            const encryptedPassword = this.cryptoService.encrypt(registrationDto.password, 0);
            if (!encryptedPassword) {
                throw new common_1.BadRequestException('Erro ao criptografar senha');
            }
            const now = new Date();
            const usuario = this.usuarioRepository.create({
                nomeUsuario: registrationDto.name,
                email: encryptedEmail,
                senha: encryptedPassword,
                telefone: registrationDto.telefone,
                empresaId: empresa.id,
                isExcluido: 'N',
                dataHoraUsuarioInc: now,
                dataHoraUsuarioAlt: now,
                usuarioInc: 'registro-sistema',
                usuarioAlt: 'registro-sistema',
                admGeral: true,
                admLocal: true,
                operador: true,
                operadorEstoque: false,
                visualizaSaldosBancarios: true,
                gerenciarLctosFinanceiros: true,
                relatoriosFinanceiros: true,
                relatoriosComandas: true,
                gerenciarProdutos: true,
                podeAcessarFiscal: true,
                podeAcessarConfiguracoes: true,
                twoAuthentication: false,
                podeVisualizarCatFinOculto: true,
                desconectarUsuarios: true,
                podeGerarExcelFinanceiro: true,
                podeImprimirLancamentosFinanceiro: true,
                usuarioLogado: false,
                aceitaTermoUso: true,
                dataAceitaTermoUso: now,
            });
            const savedUser = await this.usuarioRepository.save(usuario);
            return await this.authenticateUser(savedUser);
        }
        catch (error) {
            console.error('Erro no registro:', error);
            if (error instanceof common_1.ConflictException ||
                error instanceof common_1.BadRequestException) {
                return {
                    erro: error.message,
                    mensagem: error.message,
                    tipo: 'danger',
                };
            }
            return {
                erro: 'Erro ao realizar cadastro',
                mensagem: 'Erro interno do servidor',
                tipo: 'danger',
            };
        }
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(usuario_entity_1.Usuario)),
    __param(1, (0, typeorm_1.InjectRepository)(empresa_entity_1.Empresa)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        jwt_1.JwtService,
        legacy_crypto_service_1.LegacyCryptoService,
        email_service_1.EmailService,
        empresa_service_1.EmpresaService])
], AuthService);
//# sourceMappingURL=auth.service.js.map