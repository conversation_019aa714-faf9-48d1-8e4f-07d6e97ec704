export declare class FornecedorResponseDto {
    id: number;
    uuid?: string;
    descricao: string;
    cpf?: string;
    cnpj?: string;
    inscricaoEstadual?: string;
    inscricaoMunicipal?: string;
    prazoEntrega?: number;
    prazoPagamento?: string;
    formaPagamento?: string;
    empresaId: number;
    enderecoId?: number;
    planoContaId?: number;
    isExcluido: string;
    dataHoraUsuarioInc: Date;
    dataHoraUsuarioAlt: Date;
    dataHoraUsuarioDel?: Date;
    usuarioInc: string;
    usuarioAlt: string;
    usuarioDel?: string;
    dataSync?: Date;
}
export declare class PaginatedFornecedorResponseDto {
    data: FornecedorResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
