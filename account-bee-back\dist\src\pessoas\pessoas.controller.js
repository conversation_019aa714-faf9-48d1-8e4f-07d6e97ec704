"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PessoasController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const pessoas_service_1 = require("./pessoas.service");
const create_pessoa_dto_1 = require("./dto/create-pessoa.dto");
const update_pessoa_dto_1 = require("./dto/update-pessoa.dto");
const pessoa_response_dto_1 = require("./dto/pessoa-response.dto");
const filter_pessoas_dto_1 = require("./dto/filter-pessoas.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../common/guards/roles.guard");
const permissions_guard_1 = require("../common/guards/permissions.guard");
const roles_decorator_1 = require("../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../common/decorators/permissions.decorator");
const user_roles_enum_1 = require("../common/enums/user-roles.enum");
let PessoasController = class PessoasController {
    pessoasService;
    constructor(pessoasService) {
        this.pessoasService = pessoasService;
    }
    create(createPessoaDto, req) {
        return this.pessoasService.create(createPessoaDto, req.user);
    }
    findAll(filterDto, req) {
        return this.pessoasService.findAll(filterDto, req.user);
    }
    findByEmpresa(empresaId, req) {
        return this.pessoasService.findByEmpresa(+empresaId, req.user);
    }
    findOne(id, req) {
        return this.pessoasService.findOne(+id, req.user);
    }
    update(id, updatePessoaDto, req) {
        return this.pessoasService.update(+id, updatePessoaDto, req.user);
    }
    toggleStatus(id, req) {
        return this.pessoasService.toggleStatus(+id, req.user);
    }
    remove(id, req) {
        return this.pessoasService.remove(+id, req.user);
    }
};
exports.PessoasController = PessoasController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(user_roles_enum_1.UserRole.ADMIN_GERAL, user_roles_enum_1.UserRole.ADMIN_LOCAL, user_roles_enum_1.UserRole.OPERADOR),
    (0, permissions_decorator_1.RequirePermissions)(user_roles_enum_1.UserPermission.USERS_CREATE),
    (0, swagger_1.ApiOperation)({ summary: 'Criar nova pessoa' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Pessoa criada com sucesso',
        type: pessoa_response_dto_1.PessoaResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Email, CPF ou CNPJ já cadastrado' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Sem permissão' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_pessoa_dto_1.CreatePessoaDto, Object]),
    __metadata("design:returntype", void 0)
], PessoasController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, permissions_decorator_1.RequirePermissions)(user_roles_enum_1.UserPermission.USERS_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Listar pessoas com filtros e paginação' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de pessoas',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/PessoaResponseDto' },
                },
                total: { type: 'number' },
                page: { type: 'number' },
                limit: { type: 'number' },
            },
        },
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [filter_pessoas_dto_1.FilterPessoasDto, Object]),
    __metadata("design:returntype", void 0)
], PessoasController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('empresa/:empresaId'),
    (0, permissions_decorator_1.RequirePermissions)(user_roles_enum_1.UserPermission.USERS_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Listar pessoas por empresa' }),
    (0, swagger_1.ApiParam)({ name: 'empresaId', description: 'ID da empresa' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de pessoas da empresa',
        type: [pessoa_response_dto_1.PessoaResponseDto],
    }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Sem permissão' }),
    __param(0, (0, common_1.Param)('empresaId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], PessoasController.prototype, "findByEmpresa", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, permissions_decorator_1.RequirePermissions)(user_roles_enum_1.UserPermission.USERS_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar pessoa por ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID da pessoa' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dados da pessoa',
        type: pessoa_response_dto_1.PessoaResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Pessoa não encontrada' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Sem permissão' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], PessoasController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)(user_roles_enum_1.UserRole.ADMIN_GERAL, user_roles_enum_1.UserRole.ADMIN_LOCAL, user_roles_enum_1.UserRole.OPERADOR),
    (0, permissions_decorator_1.RequirePermissions)(user_roles_enum_1.UserPermission.USERS_UPDATE),
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar pessoa' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID da pessoa' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Pessoa atualizada com sucesso',
        type: pessoa_response_dto_1.PessoaResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Pessoa não encontrada' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Sem permissão' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Email, CPF ou CNPJ já cadastrado' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_pessoa_dto_1.UpdatePessoaDto, Object]),
    __metadata("design:returntype", void 0)
], PessoasController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    (0, roles_decorator_1.Roles)(user_roles_enum_1.UserRole.ADMIN_GERAL, user_roles_enum_1.UserRole.ADMIN_LOCAL),
    (0, permissions_decorator_1.RequirePermissions)(user_roles_enum_1.UserPermission.USERS_UPDATE),
    (0, swagger_1.ApiOperation)({ summary: 'Ativar/desativar pessoa' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID da pessoa' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Status alterado com sucesso',
        type: pessoa_response_dto_1.PessoaResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Pessoa não encontrada' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Sem permissão' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], PessoasController.prototype, "toggleStatus", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, roles_decorator_1.Roles)(user_roles_enum_1.UserRole.ADMIN_GERAL, user_roles_enum_1.UserRole.ADMIN_LOCAL),
    (0, permissions_decorator_1.RequirePermissions)(user_roles_enum_1.UserPermission.USERS_DELETE),
    (0, swagger_1.ApiOperation)({ summary: 'Excluir pessoa (soft delete)' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID da pessoa' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Pessoa excluída com sucesso' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Pessoa não encontrada' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Sem permissão' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], PessoasController.prototype, "remove", null);
exports.PessoasController = PessoasController = __decorate([
    (0, swagger_1.ApiTags)('pessoas'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard, permissions_guard_1.PermissionsGuard),
    (0, common_1.Controller)('pessoas'),
    __metadata("design:paramtypes", [pessoas_service_1.PessoasService])
], PessoasController);
//# sourceMappingURL=pessoas.controller.js.map