import { Repository } from 'typeorm';
import { Fornecedor } from '../entities/fornecedor.entity';
import { FilterFornecedorDto } from './dto/filter-fornecedor.dto';
export declare class FornecedorRepository {
    private readonly fornecedorRepository;
    constructor(fornecedorRepository: Repository<Fornecedor>);
    create(fornecedorData: Partial<Fornecedor>): Promise<Fornecedor>;
    findAll(empresaId: number, filterDto: FilterFornecedorDto): Promise<{
        data: Fornecedor[];
        total: number;
    }>;
    findById(id: number, empresaId?: number): Promise<Fornecedor | null>;
    update(id: number, updateData: Partial<Fornecedor>): Promise<Fornecedor>;
    delete(id: number, usuarioEmail: string): Promise<void>;
    findByCpf(cpf: string, empresaId: number): Promise<Fornecedor | null>;
    findByCnpj(cnpj: string, empresaId: number): Promise<Fornecedor | null>;
    findByDescricao(descricao: string, empresaId: number): Promise<Fornecedor | null>;
    private createQueryBuilder;
    private applyFilters;
    private applyDefaultSorting;
    findAllByEmpresa(empresaId: number): Promise<Fornecedor[]>;
}
