"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LancamentosFinanceirosModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const lancamento_financeiro_entity_1 = require("../entities/lancamento-financeiro.entity");
const financeiro_centro_custo_entity_1 = require("../entities/financeiro-centro-custo.entity");
const tipo_lancamento_financeiro_entity_1 = require("../entities/tipo-lancamento-financeiro.entity");
const categoria_lancamento_entity_1 = require("../entities/categoria-lancamento.entity");
const plano_conta_entity_1 = require("../entities/plano-conta.entity");
const lancamentos_financeiros_controller_1 = require("./lancamentos-financeiros.controller");
const lancamentos_financeiros_service_1 = require("./lancamentos-financeiros.service");
const crypto_module_1 = require("../crypto/crypto.module");
let LancamentosFinanceirosModule = class LancamentosFinanceirosModule {
};
exports.LancamentosFinanceirosModule = LancamentosFinanceirosModule;
exports.LancamentosFinanceirosModule = LancamentosFinanceirosModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                lancamento_financeiro_entity_1.LancamentoFinanceiro,
                financeiro_centro_custo_entity_1.FinanceiroCentroCusto,
                tipo_lancamento_financeiro_entity_1.TipoLancamentoFinanceiro,
                categoria_lancamento_entity_1.CategoriaLancamento,
                plano_conta_entity_1.PlanoConta,
            ]),
            crypto_module_1.CryptoModule,
        ],
        controllers: [lancamentos_financeiros_controller_1.LancamentosFinanceirosController],
        providers: [lancamentos_financeiros_service_1.LancamentosFinanceirosService],
        exports: [lancamentos_financeiros_service_1.LancamentosFinanceirosService],
    })
], LancamentosFinanceirosModule);
//# sourceMappingURL=lancamentos-financeiros.module.js.map