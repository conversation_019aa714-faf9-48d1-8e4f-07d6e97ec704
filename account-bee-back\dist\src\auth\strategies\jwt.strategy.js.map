{"version": 3, "file": "jwt.strategy.js", "sourceRoot": "", "sources": ["../../../../src/auth/strategies/jwt.strategy.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAmE;AACnE,+CAAoD;AACpD,+CAAoD;AACpD,2CAA+C;AAC/C,6CAAmD;AACnD,qCAAqC;AACrC,kEAAwD;AAGjD,IAAM,WAAW,GAAjB,MAAM,WAAY,SAAQ,IAAA,2BAAgB,EAAC,uBAAQ,CAAC;IAI/C;IAHV,YACE,aAA4B,EAEpB,iBAAsC;QAE9C,MAAM,SAAS,GAAG,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,gBAAgB,CAAC;QACtE,OAAO,CAAC,GAAG,CACT,uCAAuC,EACvC,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CACnC,CAAC;QAEF,KAAK,CAAC;YACJ,cAAc,EAAE,yBAAU,CAAC,2BAA2B,EAAE;YACxD,gBAAgB,EAAE,KAAK;YACvB,WAAW,EAAE,SAAS;SACvB,CAAC,CAAC;QAZK,sBAAiB,GAAjB,iBAAiB,CAAqB;IAahD,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAY;QACzB,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC;QAG1D,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;YAChD,KAAK,EAAE;gBACL,EAAE,EAAE,OAAO,CAAC,GAAG;gBACf,UAAU,EAAE,GAAG;aAChB;SACF,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CACT,aAAa,EACb,IAAI,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,EAAE,eAAe,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,MAAM,CAC7D,CAAC;QAEF,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,8BAAqB,CAAC,mCAAmC,CAAC,CAAC;QACvE,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF,CAAA;AAzCY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;IAIR,WAAA,IAAA,0BAAgB,EAAC,wBAAO,CAAC,CAAA;qCADX,sBAAa;QAED,oBAAU;GAJ5B,WAAW,CAyCvB"}