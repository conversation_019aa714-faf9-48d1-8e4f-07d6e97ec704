/* Import responsive utilities */
@import './styles/responsive.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Account Bee - Tema Financeiro com Amarelo Suave */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Amarelo suave como cor primária */
    --primary: 48 96% 53%;
    --primary-foreground: 48 95% 8%;
    --primary-glow: 48 100% 67%;

    /* Cinza neutro para secundário */
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    /* <PERSON><PERSON> mais sutil para accent */
    --accent: 48 100% 96%;
    --accent-foreground: 48 95% 15%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --success: 142 76% 36%;
    --success-foreground: 210 40% 98%;

    --warning: 38 92% 50%;
    --warning-foreground: 222.2 84% 4.9%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 48 96% 53%;

    /* Gradientes com amarelo */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-subtle: linear-gradient(180deg, hsl(var(--background)), hsl(var(--accent)));
    --gradient-financial: linear-gradient(135deg, hsl(48 100% 97%), hsl(48 100% 94%));

    /* Sombras elegantes */
    --shadow-elegant: 0 10px 30px -10px hsl(var(--primary) / 0.15);
    --shadow-glow: 0 0 40px hsl(var(--primary-glow) / 0.20);
    --shadow-bee: 0 4px 20px -2px hsl(48 96% 53% / 0.1);

    /* Animações */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Preto suave e confortável para dark mode */
    --background: 220 13% 9%;
    --foreground: 220 13% 96%;

    --card: 220 13% 11%;
    --card-foreground: 220 13% 96%;

    --popover: 220 13% 11%;
    --popover-foreground: 220 13% 96%;

    /* Amarelo como cor primária no dark mode */
    --primary: 48 96% 53%;
    --primary-foreground: 48 95% 8%;
    --primary-glow: 48 100% 67%;

    --secondary: 220 13% 16%;
    --secondary-foreground: 220 13% 96%;

    --muted: 220 13% 14%;
    --muted-foreground: 220 9% 60%;

    --accent: 220 13% 16%;
    --accent-foreground: 220 13% 96%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 220 13% 96%;

    --success: 142 76% 36%;
    --success-foreground: 220 13% 96%;

    --warning: 38 92% 50%;
    --warning-foreground: 220 13% 9%;

    --border: 220 13% 16%;
    --input: 220 13% 16%;
    --ring: 48 96% 53%;
    --sidebar-background: 220 13% 12%;
    --sidebar-foreground: 220 13% 90%;
    --sidebar-primary: 48 96% 53%;
    --sidebar-primary-foreground: 48 95% 8%;
    --sidebar-accent: 220 13% 16%;
    --sidebar-accent-foreground: 220 13% 90%;
    --sidebar-border: 220 13% 16%;
    --sidebar-ring: 48 96% 53%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Improved Table Styles */
@layer components {
  /* Custom Scrollbar for Tables */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground) / 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 3px;
    transition: background-color 0.2s ease;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }

  .custom-scrollbar::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* Table Enhanced Styles */
  .table-container {
    position: relative;
    overflow: hidden;
    border-radius: calc(var(--radius) - 2px);
    border: 1px solid hsl(var(--border));
  }

  /* Improved Table Row Hover */
  .table-row-enhanced {
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
    border-bottom: 1px solid hsl(var(--border) / 0.4);
  }

  .table-row-enhanced:hover {
    background-color: hsl(var(--muted) / 0.3);
    border-bottom-color: hsl(var(--border) / 0.6);
  }

  .table-row-enhanced:last-child {
    border-bottom: none;
  }

  /* Enhanced Table Header */
  .table-header-enhanced {
    background: linear-gradient(
      180deg,
      hsl(var(--background) / 0.95),
      hsl(var(--muted) / 0.1)
    );
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border-bottom: 1px solid hsl(var(--border) / 0.8);
    box-shadow: 0 1px 3px hsl(var(--foreground) / 0.05);
  }

  /* Sortable Column Indicator */
  .sortable-column {
    position: relative;
    user-select: none;
    cursor: pointer;
    transition: color 0.15s ease;
  }

  .sortable-column:hover {
    color: hsl(var(--foreground));
  }

  .sortable-column::after {
    content: '';
    position: absolute;
    right: -12px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    opacity: 0.5;
    transition: opacity 0.15s ease;
  }

  .sortable-column:hover::after {
    opacity: 0.8;
  }

  /* Loading State */
  .table-loading {
    position: relative;
    overflow: hidden;
  }

  .table-loading::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(
      90deg,
      transparent,
      hsl(var(--primary) / 0.6),
      transparent
    );
    animation: loading-shimmer 1.5s infinite;
    z-index: 1;
  }

  @keyframes loading-shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  /* Improved Cell Contrast */
  .table-cell-enhanced {
    color: hsl(var(--foreground) / 0.9);
    transition: color 0.15s ease;
  }

  .table-cell-numeric {
    font-variant-numeric: tabular-nums;
    font-feature-settings: "tnum" on;
  }

  /* Empty State */
  .table-empty-state {
    padding: 3rem 1rem;
    text-align: center;
    color: hsl(var(--muted-foreground));
    background: linear-gradient(
      135deg,
      hsl(var(--muted) / 0.1),
      hsl(var(--muted) / 0.05)
    );
  }

  /* Accessibility Improvements */
  .table-focusable:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
    border-radius: 2px;
  }

  /* Dark Mode Specific Enhancements */
  .dark .table-header-enhanced {
    background: linear-gradient(
      180deg,
      hsl(var(--background) / 0.95),
      hsl(var(--muted) / 0.15)
    );
    border-bottom-color: hsl(var(--border) / 0.6);
    box-shadow: 0 1px 3px hsl(var(--foreground) / 0.1);
  }

  .dark .table-row-enhanced:hover {
    background-color: hsl(var(--muted) / 0.2);
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.4);
  }

  .dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.6);
  }
}