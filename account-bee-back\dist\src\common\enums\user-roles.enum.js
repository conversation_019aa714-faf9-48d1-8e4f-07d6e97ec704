"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserPermission = exports.UserRole = void 0;
var UserRole;
(function (UserRole) {
    UserRole["ADMIN_GERAL"] = "admin_geral";
    UserRole["ADMIN_LOCAL"] = "admin_local";
    UserRole["OPERADOR"] = "operador";
    UserRole["OPERADOR_ESTOQUE"] = "operador_estoque";
})(UserRole || (exports.UserRole = UserRole = {}));
var UserPermission;
(function (UserPermission) {
    UserPermission["USERS_CREATE"] = "users.create";
    UserPermission["USERS_READ"] = "users.read";
    UserPermission["USERS_UPDATE"] = "users.update";
    UserPermission["USERS_DELETE"] = "users.delete";
    UserPermission["USERS_MANAGE"] = "users.manage";
    UserPermission["FINANCEIRO_VIEW_SALDOS"] = "financeiro.view_saldos";
    UserPermission["FINANCEIRO_MANAGE_LANCAMENTOS"] = "financeiro.manage_lancamentos";
    UserPermission["FINANCEIRO_REPORTS"] = "financeiro.reports";
    UserPermission["PRODUTOS_MANAGE"] = "produtos.manage";
    UserPermission["FISCAL_ACCESS"] = "fiscal.access";
    UserPermission["CONFIG_ACCESS"] = "config.access";
    UserPermission["COMANDAS_REPORTS"] = "comandas.reports";
})(UserPermission || (exports.UserPermission = UserPermission = {}));
//# sourceMappingURL=user-roles.enum.js.map