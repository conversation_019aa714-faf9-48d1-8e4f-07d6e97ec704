{"version": 3, "file": "cpf.validator.js", "sourceRoot": "", "sources": ["../../../../src/common/validators/cpf.validator.ts"], "names": [], "mappings": ";;;;;;;;;AAgDA,sBAUC;AA1DD,qDAMyB;AAGlB,IAAM,eAAe,GAArB,MAAM,eAAe;IAC1B,QAAQ,CAAC,GAAW,EAAE,IAAyB;QAC7C,IAAI,CAAC,GAAG;YAAE,OAAO,IAAI,CAAC;QAGtB,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAGxC,IAAI,QAAQ,CAAC,MAAM,KAAK,EAAE;YAAE,OAAO,KAAK,CAAC;QAGzC,IAAI,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC;YAAE,OAAO,KAAK,CAAC;QAGhD,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3B,GAAG,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QACjD,CAAC;QACD,IAAI,MAAM,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;QAC7B,IAAI,MAAM,KAAK,EAAE,IAAI,MAAM,KAAK,EAAE;YAAE,MAAM,GAAG,CAAC,CAAC;QAC/C,IAAI,MAAM,KAAK,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAAE,OAAO,KAAK,CAAC;QAG1D,GAAG,GAAG,CAAC,CAAC;QACR,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5B,GAAG,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QACjD,CAAC;QACD,IAAI,MAAM,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;QAC7B,IAAI,MAAM,KAAK,EAAE,IAAI,MAAM,KAAK,EAAE;YAAE,MAAM,GAAG,CAAC,CAAC;QAC/C,IAAI,MAAM,KAAK,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAAE,OAAO,KAAK,CAAC;QAE3D,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc,CAAC,IAAyB;QACtC,OAAO,cAAc,CAAC;IACxB,CAAC;CACF,CAAA;AArCY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,qCAAmB,EAAC,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;GACzB,eAAe,CAqC3B;AAED,SAAgB,KAAK,CAAC,iBAAqC;IACzD,OAAO,UAAU,MAAc,EAAE,YAAoB;QACnD,IAAA,mCAAiB,EAAC;YAChB,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,OAAO,EAAE,iBAAiB;YAC1B,WAAW,EAAE,EAAE;YACf,SAAS,EAAE,eAAe;SAC3B,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC"}