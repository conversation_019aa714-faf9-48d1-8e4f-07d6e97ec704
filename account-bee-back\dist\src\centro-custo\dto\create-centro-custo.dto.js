"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCentroCustoDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateCentroCustoDto {
    descricao;
    eap;
}
exports.CreateCentroCustoDto = CreateCentroCustoDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Descrição do centro de custo',
        example: 'Administrativo',
        maxLength: 200,
    }),
    (0, class_validator_1.IsString)({ message: 'Descrição deve ser um texto' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Descrição é obrigatória' }),
    (0, class_validator_1.Length)(1, 200, { message: 'Descrição deve ter entre 1 e 200 caracteres' }),
    __metadata("design:type", String)
], CreateCentroCustoDto.prototype, "descricao", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Código EAP do centro de custo',
        example: 'ADM-001',
        maxLength: 200,
    }),
    (0, class_validator_1.IsString)({ message: 'EAP deve ser um texto' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'EAP é obrigatório' }),
    (0, class_validator_1.Length)(1, 200, { message: 'EAP deve ter entre 1 e 200 caracteres' }),
    __metadata("design:type", String)
], CreateCentroCustoDto.prototype, "eap", void 0);
//# sourceMappingURL=create-centro-custo.dto.js.map