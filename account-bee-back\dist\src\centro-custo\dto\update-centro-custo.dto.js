"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateCentroCustoDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const create_centro_custo_dto_1 = require("./create-centro-custo.dto");
class UpdateCentroCustoDto extends (0, swagger_1.PartialType)(create_centro_custo_dto_1.CreateCentroCustoDto) {
    descricao;
    eap;
}
exports.UpdateCentroCustoDto = UpdateCentroCustoDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Descrição do centro de custo',
        example: 'Administrativo Atualizado',
        required: false,
        maxLength: 200,
    }),
    __metadata("design:type", String)
], UpdateCentroCustoDto.prototype, "descricao", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Código EAP do centro de custo',
        example: 'ADM-002',
        required: false,
        maxLength: 200,
    }),
    __metadata("design:type", String)
], UpdateCentroCustoDto.prototype, "eap", void 0);
//# sourceMappingURL=update-centro-custo.dto.js.map