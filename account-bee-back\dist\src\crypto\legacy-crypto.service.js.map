{"version": 3, "file": "legacy-crypto.service.js", "sourceRoot": "", "sources": ["../../../src/crypto/legacy-crypto.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,+CAAiC;AACjC,kDAAoC;AAG7B,IAAM,mBAAmB,GAAzB,MAAM,mBAAmB;IACb,kBAAkB,GAAG,mBAAmB,CAAC;IACzC,SAAS,GAAG,aAAa,CAAC;IAMnC,KAAK,CAAC,SAAiB;QAC7B,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;QAE/B,MAAM,aAAa,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC;QAEvC,IAAI,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;YACnD,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,MAAM,SAAS,GACb,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;QAC7D,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,WAAW,CAAC,SAAiB;QACnC,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;QAC/B,IAAI,SAAS,GACX,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;QAGhE,IAAI,SAAS,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YAC1B,SAAS,GAAG,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;QAC5D,CAAC;aAAM,IAAI,SAAS,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACjC,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACzC,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAMD,OAAO,CAAC,YAAoB,EAAE,SAAiB;QAC7C,IAAI,CAAC;YAEH,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;YAC/B,MAAM,aAAa,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC;YAEvC,IAAI,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;gBACnD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;YACpE,MAAM,GAAG,GAAG,IAAI,GAAG,OAAO,CAAC;YAG3B,IAAI,GAAG,CAAC,MAAM,KAAK,EAAE,EAAE,CAAC;gBACtB,OAAO,IAAI,CAAC;YACd,CAAC;YAGD,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAG3C,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YAGrD,MAAM,MAAM,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YACtE,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAG5B,IAAI,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAC1C,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;YAGvD,OAAO,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACtC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAMD,OAAO,CAAC,YAAoB,EAAE,SAAiB;QAE7C,MAAM,UAAU,GAAG;YACjB,EAAE,IAAI,EAAE,4BAA4B,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE;YACzE;gBACE,IAAI,EAAE,wBAAwB;gBAC9B,SAAS,EAAE,aAAa;gBACxB,OAAO,EAAE,KAAK;aACf;YACD,EAAE,IAAI,EAAE,mBAAmB,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE;SAClE,CAAC;QAEF,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE,CAAC;YAClC,IAAI,CAAC;gBACH,MAAM,GAAG,GACP,QAAQ,CAAC,SAAS,KAAK,OAAO;oBAC5B,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC;oBACvB,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;gBAGlC,IAAI,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;gBAGhD,IAAI,QAAQ,CAAC,OAAO,IAAI,SAAS,CAAC,MAAM,KAAK,EAAE,EAAE,CAAC;oBAChD,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3D,CAAC;gBAGD,IAAI,SAAS,CAAC,MAAM,KAAK,EAAE;oBAAE,SAAS;gBAGtC,MAAM,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;gBAG5D,MAAM,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CACtC,IAAI,CAAC,SAAS,EACd,SAAS,EACT,IAAI,CACL,CAAC;gBACF,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;gBAG9B,IAAI,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;gBACjD,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAGzD,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;gBAGvD,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC;oBAC1D,OAAO,MAAM,CAAC;gBAChB,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBAEf,SAAS;YACX,CAAC;QACH,CAAC;QAGD,OAAO,IAAI,CAAC;IACd,CAAC;IAKD,KAAK,CAAC,OAAO,CACX,aAAqB,EACrB,iBAAyB,EACzB,SAAiB;QAEjB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC;YAC7D,OAAO,SAAS,KAAK,aAAa,CAAC;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,iBAAiB;QACf,MAAM,UAAU,GAAG,kBAAkB,CAAC;QACtC,MAAM,SAAS,GAAG,CAAC,CAAC;QAEpB,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAEtD,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YACrD,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,UAAU,KAAK,SAAS,CAAC,CAAC;QAC/D,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;CACF,CAAA;AAlLY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;GACA,mBAAmB,CAkL/B"}