"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocalResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
class LocalResponseDto {
    id;
    idEmpresa;
    nome;
    endereco;
    numero;
    bairro;
    cep;
    isExcluido;
    dataHoraUsuarioInc;
    dataHoraUsuarioAlt;
    dataHoraUsuarioDel;
}
exports.LocalResponseDto = LocalResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID único do local',
        example: 1,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], LocalResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da empresa proprietária',
        example: 1,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], LocalResponseDto.prototype, "idEmpresa", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome/descrição do local',
        example: 'Loja Centro - São Paulo',
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ obj }) => obj.descricao),
    __metadata("design:type", String)
], LocalResponseDto.prototype, "nome", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Endereço do local',
        example: 'Rua das Flores, 123',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], LocalResponseDto.prototype, "endereco", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Número do endereço',
        example: '123A',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], LocalResponseDto.prototype, "numero", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Bairro do local',
        example: 'Centro',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], LocalResponseDto.prototype, "bairro", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'CEP do local',
        example: '12345-678',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], LocalResponseDto.prototype, "cep", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status do local (N=ativo, S=inativo)',
        example: 'N',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], LocalResponseDto.prototype, "isExcluido", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de criação',
        example: '2024-01-15T10:30:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], LocalResponseDto.prototype, "dataHoraUsuarioInc", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data da última alteração',
        example: '2024-01-15T10:30:00Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], LocalResponseDto.prototype, "dataHoraUsuarioAlt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de exclusão',
        example: '2024-01-15T10:30:00Z',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], LocalResponseDto.prototype, "dataHoraUsuarioDel", void 0);
//# sourceMappingURL=local-response.dto.js.map