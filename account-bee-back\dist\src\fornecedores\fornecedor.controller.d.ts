import { FornecedorService } from './fornecedor.service';
import { CreateFornecedorDto } from './dto/create-fornecedor.dto';
import { UpdateFornecedorDto } from './dto/update-fornecedor.dto';
import { FornecedorResponseDto, PaginatedFornecedorResponseDto } from './dto/fornecedor-response.dto';
import { FornecedorDropdownDto } from '../lancamentos-financeiros/dto';
import { FilterFornecedorDto } from './dto/filter-fornecedor.dto';
export declare class FornecedorController {
    private readonly fornecedorService;
    constructor(fornecedorService: FornecedorService);
    create(createFornecedorDto: CreateFornecedorDto, req: any): Promise<FornecedorResponseDto>;
    findAll(req: any, filterDto: FilterFornecedorDto): Promise<PaginatedFornecedorResponseDto>;
    getDropdownOptions(req: any): Promise<FornecedorDropdownDto[]>;
    findOne(id: string, req: any): Promise<FornecedorResponseDto>;
    update(id: string, updateFornecedorDto: UpdateFornecedorDto, req: any): Promise<FornecedorResponseDto>;
    remove(id: string, req: any): Promise<void>;
}
