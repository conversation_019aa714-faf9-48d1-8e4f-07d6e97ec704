"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FilterCentroCustoDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class FilterCentroCustoDto {
    descricao;
    eap;
    status;
    uuid;
}
exports.FilterCentroCustoDto = FilterCentroCustoDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Filtrar por descrição do centro de custo',
        example: 'Administrativo',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Descrição deve ser um texto' }),
    __metadata("design:type", String)
], FilterCentroCustoDto.prototype, "descricao", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Filtrar por código EAP',
        example: 'ADM-001',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'EAP deve ser um texto' }),
    __metadata("design:type", String)
], FilterCentroCustoDto.prototype, "eap", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Filtrar por status (ativo ou excluído)',
        example: 'ativo',
        enum: ['ativo', 'excluido', 'todos'],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsIn)(['ativo', 'excluido', 'todos'], {
        message: 'Status deve ser: ativo, excluido ou todos',
    }),
    __metadata("design:type", String)
], FilterCentroCustoDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'UUID para filtro de sincronização',
        example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'UUID deve ser um texto' }),
    __metadata("design:type", String)
], FilterCentroCustoDto.prototype, "uuid", void 0);
//# sourceMappingURL=filter-centro-custo.dto.js.map