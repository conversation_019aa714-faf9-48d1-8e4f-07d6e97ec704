"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CentroCustoResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class CentroCustoResponseDto {
    id;
    descricao;
    eap;
    empresaId;
    dataHoraUsuarioAlt;
    dataHoraUsuarioDel;
    dataHoraUsuarioInc;
    dataSync;
    isExcluido;
    usuarioAlt;
    usuarioDel;
    usuarioInc;
    uuid;
}
exports.CentroCustoResponseDto = CentroCustoResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID do centro de custo',
        example: 1,
    }),
    __metadata("design:type", Number)
], CentroCustoResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Descrição do centro de custo',
        example: 'Administrativo',
    }),
    __metadata("design:type", String)
], CentroCustoResponseDto.prototype, "descricao", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Código EAP do centro de custo',
        example: 'ADM-001',
    }),
    __metadata("design:type", String)
], CentroCustoResponseDto.prototype, "eap", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da empresa',
        example: 1,
    }),
    __metadata("design:type", Number)
], CentroCustoResponseDto.prototype, "empresaId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data e hora da última alteração',
        example: '2024-01-15T10:30:00.000Z',
    }),
    __metadata("design:type", Date)
], CentroCustoResponseDto.prototype, "dataHoraUsuarioAlt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data e hora da exclusão lógica',
        example: null,
        required: false,
    }),
    __metadata("design:type", Date)
], CentroCustoResponseDto.prototype, "dataHoraUsuarioDel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data e hora da criação',
        example: '2024-01-15T10:30:00.000Z',
    }),
    __metadata("design:type", Date)
], CentroCustoResponseDto.prototype, "dataHoraUsuarioInc", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de sincronização',
        example: null,
        required: false,
    }),
    __metadata("design:type", Date)
], CentroCustoResponseDto.prototype, "dataSync", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Indica se o registro foi excluído logicamente',
        example: 'N',
    }),
    __metadata("design:type", String)
], CentroCustoResponseDto.prototype, "isExcluido", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'E-mail do usuário que alterou o registro',
        example: '<EMAIL>',
    }),
    __metadata("design:type", String)
], CentroCustoResponseDto.prototype, "usuarioAlt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'E-mail do usuário que excluiu o registro',
        example: null,
        required: false,
    }),
    __metadata("design:type", String)
], CentroCustoResponseDto.prototype, "usuarioDel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'E-mail do usuário que criou o registro',
        example: '<EMAIL>',
    }),
    __metadata("design:type", String)
], CentroCustoResponseDto.prototype, "usuarioInc", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'UUID para sincronização',
        example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
        required: false,
    }),
    __metadata("design:type", String)
], CentroCustoResponseDto.prototype, "uuid", void 0);
//# sourceMappingURL=centro-custo-response.dto.js.map