import { Repository } from 'typeorm';
import { Local } from '../entities/local.entity';
import { Empresa } from '../entities/empresa.entity';
import { Usuario } from '../entities/usuario.entity';
import { CreateLocalDto } from './dto/create-local.dto';
import { UpdateLocalDto } from './dto/update-local.dto';
import { FilterLocaisDto } from './dto/filter-locais.dto';
import { LocalResponseDto } from './dto/local-response.dto';
import { LegacyCryptoService } from '../crypto/legacy-crypto.service';
import { BaseService } from '../common/services/base.service';
export declare class LocaisService extends BaseService {
    private readonly localRepository;
    private readonly empresaRepository;
    private readonly cryptoService;
    constructor(localRepository: Repository<Local>, empresaRepository: Repository<Empresa>, cryptoService: LegacyCryptoService);
    create(createLocalDto: CreateLocalDto, currentUser: Usuario): Promise<LocalResponseDto>;
    findAll(filterDto: FilterLocaisDto, currentUser: Usuario): Promise<{
        data: LocalResponseDto[];
        total: number;
        page: number;
        limit: number;
    }>;
    findOne(id: number, currentUser: Usuario): Promise<LocalResponseDto>;
    update(id: number, updateLocalDto: UpdateLocalDto, currentUser: Usuario): Promise<LocalResponseDto>;
    remove(id: number, currentUser: Usuario): Promise<void>;
    toggleStatus(id: number, currentUser: Usuario): Promise<LocalResponseDto>;
    findByEmpresa(idEmpresa: number, currentUser: Usuario): Promise<LocalResponseDto[]>;
    private checkDuplicates;
    private encryptSensitiveData;
    private prepareLocalResponse;
    private decryptField;
    private normalizeSensitiveData;
}
