"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LancamentosFinanceirosService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const lancamento_financeiro_entity_1 = require("../entities/lancamento-financeiro.entity");
const financeiro_centro_custo_entity_1 = require("../entities/financeiro-centro-custo.entity");
const financial_crypto_service_1 = require("../crypto/financial-crypto.service");
let LancamentosFinanceirosService = class LancamentosFinanceirosService {
    lancamentoRepository;
    financeiroCentroCustoRepository;
    financialCryptoService;
    dataSource;
    constructor(lancamentoRepository, financeiroCentroCustoRepository, financialCryptoService, dataSource) {
        this.lancamentoRepository = lancamentoRepository;
        this.financeiroCentroCustoRepository = financeiroCentroCustoRepository;
        this.financialCryptoService = financialCryptoService;
        this.dataSource = dataSource;
    }
    async create(createDto, user) {
        const empresaId = user.empresaId;
        this.validateRequiredFields(createDto, empresaId);
        if (createDto.alocacoesCentroCusto && createDto.alocacoesCentroCusto.length > 0) {
            this.validateCostCenterAllocations(createDto.alocacoesCentroCusto, createDto.valor);
        }
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const now = new Date();
            const userInfo = JSON.stringify({
                id: user.id,
                nomeUsuario: user.name || user.email,
                email: user.email,
            });
            const valorCriptografado = this.financialCryptoService.encryptValue(createDto.valor, empresaId);
            const valorBrutoCriptografado = createDto.valorBruto
                ? this.financialCryptoService.encryptValue(createDto.valorBruto, empresaId)
                : null;
            const timestamp = now.getTime();
            const identificador = `REC-${empresaId}-${timestamp}`;
            const uuid = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
            const lancamentoData = {
                descricao: createDto.descricao,
                dataLancamento: createDto.dataLancamento ? new Date(createDto.dataLancamento) : undefined,
                dataCompetencia: createDto.dataCompetencia ? new Date(createDto.dataCompetencia) : undefined,
                valorBruto: valorBrutoCriptografado || undefined,
                valor: valorCriptografado,
                observacao: createDto.observacao,
                identificador,
                uuid,
                empresaId,
                pessoaId: createDto.pessoaId,
                contaId: createDto.contaId,
                localId: createDto.localId,
                fornecedorId: createDto.fornecedorId,
                tipoLancamentoFinanceiroId: 1,
                categoriaLctoFinanceiroId: createDto.categoriaLctoFinanceiroId,
                planoContaCredito: createDto.planoContaCredito,
                efetivado: false,
                conciliado: false,
                dataHoraUsuarioInc: now,
                dataHoraUsuarioAlt: now,
                usuarioInc: userInfo,
                usuarioAlt: userInfo,
                isExcluido: 'N',
            };
            const lancamento = queryRunner.manager.create(lancamento_financeiro_entity_1.LancamentoFinanceiro, lancamentoData);
            const savedLancamento = await queryRunner.manager.save(lancamento_financeiro_entity_1.LancamentoFinanceiro, lancamento);
            if (createDto.alocacoesCentroCusto && createDto.alocacoesCentroCusto.length > 0) {
                await this.createCostCenterAllocations(queryRunner, savedLancamento.id, createDto.alocacoesCentroCusto, empresaId, userInfo, now);
            }
            if (createDto.repetirReceita && createDto.periodicidade && createDto.quantidadeRepeticoes) {
                await this.createRecurringLancamentos(queryRunner, savedLancamento, createDto, empresaId, userInfo);
            }
            await queryRunner.commitTransaction();
            return this.mapToResponseDto(savedLancamento, empresaId);
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    validateRequiredFields(createDto, empresaId) {
        if (!empresaId) {
            throw new common_1.BadRequestException('EMPRESA_ID é obrigatório');
        }
        if (!createDto.pessoaId) {
            throw new common_1.BadRequestException('Cliente/Pessoa é obrigatório');
        }
        if (!createDto.descricao || createDto.descricao.trim().length === 0) {
            throw new common_1.BadRequestException('Descrição é obrigatória');
        }
        if (!createDto.valor || createDto.valor <= 0) {
            throw new common_1.BadRequestException('Valor líquido deve ser maior que zero');
        }
    }
    validateCostCenterAllocations(allocations, totalValue) {
        const totalPercentage = allocations.reduce((sum, allocation) => {
            return sum + (allocation.porcentagem || 0);
        }, 0);
        if (Math.abs(totalPercentage - 100) > 0.01) {
            throw new common_1.BadRequestException('A soma dos percentuais de alocação deve ser igual a 100%');
        }
        const totalAllocatedValue = allocations.reduce((sum, allocation) => {
            return sum + allocation.valor;
        }, 0);
        if (Math.abs(totalAllocatedValue - totalValue) > 0.01) {
            throw new common_1.BadRequestException('A soma dos valores de alocação deve ser igual ao valor líquido');
        }
        const centroCustoIds = allocations.map(a => a.centroCustoId);
        const uniqueIds = new Set(centroCustoIds);
        if (uniqueIds.size !== centroCustoIds.length) {
            throw new common_1.BadRequestException('Não é possível alocar o mesmo centro de custo múltiplas vezes');
        }
    }
    async createCostCenterAllocations(queryRunner, lancamentoId, allocations, empresaId, userInfo, now) {
        for (const allocation of allocations) {
            const valorCriptografado = this.financialCryptoService.encryptValue(allocation.valor, empresaId);
            const uuidAlocacao = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}-${allocation.centroCustoId}`;
            const financeiroCentroCusto = queryRunner.manager.create(financeiro_centro_custo_entity_1.FinanceiroCentroCusto, {
                lancamentoFinanceiroId: lancamentoId,
                centroCustoId: allocation.centroCustoId,
                valor: valorCriptografado,
                porcentagem: allocation.porcentagem,
                uuid: uuidAlocacao,
                dataHoraUsuarioInc: now,
                dataHoraUsuarioAlt: now,
                usuarioInc: userInfo,
                usuarioAlt: userInfo,
                isExcluido: 'N',
            });
            await queryRunner.manager.save(financeiro_centro_custo_entity_1.FinanceiroCentroCusto, financeiroCentroCusto);
        }
    }
    mapToResponseDto(lancamento, empresaId) {
        return {
            id: lancamento.id,
            descricao: lancamento.descricao,
            dataLancamento: lancamento.dataLancamento,
            dataCompetencia: lancamento.dataCompetencia,
            valorBruto: lancamento.valorBruto
                ? this.financialCryptoService.decryptValueAsNumber(lancamento.valorBruto, empresaId)
                : undefined,
            valor: lancamento.valor
                ? this.financialCryptoService.decryptValueAsNumber(lancamento.valor, empresaId)
                : 0,
            observacao: lancamento.observacao,
            efetivado: lancamento.efetivado,
            conciliado: lancamento.conciliado,
            empresaId: lancamento.empresaId,
            pessoaId: lancamento.pessoaId,
            contaId: lancamento.contaId,
            localId: lancamento.localId,
            fornecedorId: lancamento.fornecedorId,
            tipoLancamentoFinanceiroId: lancamento.tipoLancamentoFinanceiroId,
            categoriaLctoFinanceiroId: lancamento.categoriaLctoFinanceiroId,
            planoContaCredito: lancamento.planoContaCredito,
            dataHoraUsuarioInc: lancamento.dataHoraUsuarioInc,
            dataHoraUsuarioAlt: lancamento.dataHoraUsuarioAlt,
            usuarioInc: lancamento.usuarioInc,
            usuarioAlt: lancamento.usuarioAlt,
        };
    }
    async findById(id, empresaId) {
        const lancamento = await this.lancamentoRepository.findOne({
            where: {
                id,
                empresaId,
                isExcluido: 'N',
            },
            relations: ['financeiroCentroCustos', 'financeiroCentroCustos.centroCusto'],
        });
        if (!lancamento) {
            throw new common_1.NotFoundException('Lançamento financeiro não encontrado');
        }
        const responseDto = this.mapToResponseDto(lancamento, empresaId);
        if (lancamento.financeiroCentroCustos && lancamento.financeiroCentroCustos.length > 0) {
            responseDto.alocacoesCentroCusto = lancamento.financeiroCentroCustos
                .filter(fcc => fcc.isExcluido === 'N')
                .map(fcc => ({
                id: fcc.id,
                centroCustoId: fcc.centroCustoId,
                centroCustoNome: fcc.centroCusto?.descricao || '',
                valor: this.financialCryptoService.decryptValueAsNumber(fcc.valor, empresaId),
                porcentagem: fcc.porcentagem,
            }));
        }
        return responseDto;
    }
    async updateCostCenterAllocations(lancamentoId, allocations, empresaId, user) {
        const lancamento = await this.lancamentoRepository.findOne({
            where: { id: lancamentoId, empresaId, isExcluido: 'N' },
        });
        if (!lancamento) {
            throw new common_1.NotFoundException('Lançamento financeiro não encontrado');
        }
        const valorLiquido = lancamento.valor
            ? this.financialCryptoService.decryptValueAsNumber(lancamento.valor, empresaId)
            : 0;
        this.validateCostCenterAllocations(allocations, valorLiquido);
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            await queryRunner.manager.update(financeiro_centro_custo_entity_1.FinanceiroCentroCusto, { lancamentoFinanceiroId: lancamentoId, isExcluido: 'N' }, {
                isExcluido: 'S',
                dataHoraUsuarioDel: new Date(),
                usuarioDel: JSON.stringify({
                    id: user.id,
                    nomeUsuario: user.name || user.email,
                    email: user.email,
                }),
            });
            const now = new Date();
            const userInfo = JSON.stringify({
                id: user.id,
                nomeUsuario: user.name || user.email,
                email: user.email,
            });
            await this.createCostCenterAllocations(queryRunner, lancamentoId, allocations, empresaId, userInfo, now);
            await queryRunner.commitTransaction();
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw error;
        }
        finally {
            await queryRunner.release();
        }
    }
    async deleteCostCenterAllocation(lancamentoId, allocationId, empresaId, user) {
        const allocation = await this.financeiroCentroCustoRepository.findOne({
            where: {
                id: allocationId,
                lancamentoFinanceiroId: lancamentoId,
                isExcluido: 'N',
            },
            relations: ['lancamentoFinanceiro'],
        });
        if (allocation && allocation.lancamentoFinanceiro.empresaId !== empresaId) {
            throw new common_1.NotFoundException('Alocação de centro de custo não encontrada');
        }
        if (!allocation) {
            throw new common_1.NotFoundException('Alocação de centro de custo não encontrada');
        }
        allocation.isExcluido = 'S';
        allocation.dataHoraUsuarioDel = new Date();
        allocation.usuarioDel = JSON.stringify({
            id: user.id,
            nomeUsuario: user.name || user.email,
            email: user.email,
        });
        await this.financeiroCentroCustoRepository.save(allocation);
    }
    async getCostCenterAllocations(lancamentoId, empresaId) {
        const allocations = await this.financeiroCentroCustoRepository.find({
            where: {
                lancamentoFinanceiroId: lancamentoId,
                isExcluido: 'N',
            },
            relations: ['centroCusto', 'lancamentoFinanceiro'],
            order: { id: 'ASC' },
        });
        const filteredAllocations = allocations.filter(allocation => allocation.lancamentoFinanceiro.empresaId === empresaId);
        return filteredAllocations.map(allocation => ({
            id: allocation.id,
            centroCustoId: allocation.centroCustoId,
            centroCustoNome: allocation.centroCusto?.descricao || '',
            valor: this.financialCryptoService.decryptValueAsNumber(allocation.valor, empresaId),
            porcentagem: allocation.porcentagem,
        }));
    }
    async createRecurringLancamentos(queryRunner, originalLancamento, createDto, empresaId, userInfo) {
        const quantidadeRepeticoes = createDto.quantidadeRepeticoes || 1;
        for (let i = 1; i <= quantidadeRepeticoes; i++) {
            const nextDate = this.calculateNextDate(originalLancamento.dataLancamento || new Date(), createDto.periodicidade, i);
            const nextCompetenceDate = originalLancamento.dataCompetencia
                ? this.calculateNextDate(originalLancamento.dataCompetencia, createDto.periodicidade, i)
                : null;
            const now = new Date();
            const timestamp = now.getTime();
            const identificadorRecorrente = `REC-${empresaId}-${timestamp}-${i}`;
            const uuidRecorrente = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}-${i}`;
            const novoLancamentoData = {
                descricao: originalLancamento.descricao,
                dataLancamento: nextDate,
                dataCompetencia: nextCompetenceDate || undefined,
                valorBruto: originalLancamento.valorBruto || undefined,
                valor: originalLancamento.valor,
                observacao: originalLancamento.observacao,
                identificador: identificadorRecorrente,
                uuid: uuidRecorrente,
                empresaId,
                pessoaId: originalLancamento.pessoaId,
                contaId: originalLancamento.contaId,
                localId: originalLancamento.localId,
                fornecedorId: originalLancamento.fornecedorId,
                tipoLancamentoFinanceiroId: 1,
                categoriaLctoFinanceiroId: originalLancamento.categoriaLctoFinanceiroId,
                planoContaCredito: originalLancamento.planoContaCredito,
                efetivado: false,
                conciliado: false,
                dataHoraUsuarioInc: now,
                dataHoraUsuarioAlt: now,
                usuarioInc: userInfo,
                usuarioAlt: userInfo,
                isExcluido: 'N',
            };
            const novoLancamento = queryRunner.manager.create(lancamento_financeiro_entity_1.LancamentoFinanceiro, novoLancamentoData);
            const savedNovoLancamento = await queryRunner.manager.save(lancamento_financeiro_entity_1.LancamentoFinanceiro, novoLancamento);
            if (createDto.alocacoesCentroCusto && createDto.alocacoesCentroCusto.length > 0) {
                await this.createCostCenterAllocations(queryRunner, savedNovoLancamento.id, createDto.alocacoesCentroCusto, empresaId, userInfo, now);
            }
        }
    }
    calculateNextDate(baseDate, periodicidade, increment) {
        const nextDate = new Date(baseDate);
        switch (periodicidade) {
            case 'mensal':
                nextDate.setMonth(nextDate.getMonth() + increment);
                break;
            case 'semanal':
                nextDate.setDate(nextDate.getDate() + (7 * increment));
                break;
            case 'anual':
                nextDate.setFullYear(nextDate.getFullYear() + increment);
                break;
            default:
                throw new common_1.BadRequestException('Periodicidade inválida');
        }
        return nextDate;
    }
};
exports.LancamentosFinanceirosService = LancamentosFinanceirosService;
exports.LancamentosFinanceirosService = LancamentosFinanceirosService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(lancamento_financeiro_entity_1.LancamentoFinanceiro)),
    __param(1, (0, typeorm_1.InjectRepository)(financeiro_centro_custo_entity_1.FinanceiroCentroCusto)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        financial_crypto_service_1.FinancialCryptoService,
        typeorm_2.DataSource])
], LancamentosFinanceirosService);
//# sourceMappingURL=lancamentos-financeiros.service.js.map