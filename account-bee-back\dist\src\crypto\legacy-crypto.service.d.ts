export declare class LegacyCryptoService {
    private readonly PADDING_CHARACTERS;
    private readonly ALGORITHM;
    private doKey;
    private doKeyPadded;
    encrypt(strToEncrypt: string, publicKey: number): string | null;
    decrypt(strToDecrypt: string, publicKey: number): string | null;
    compare(plainPassword: string, encryptedPassword: string, publicKey: number): Promise<boolean>;
    testCompatibility(): void;
}
