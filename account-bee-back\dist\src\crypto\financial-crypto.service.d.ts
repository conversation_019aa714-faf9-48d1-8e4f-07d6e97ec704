import { LegacyCryptoService } from './legacy-crypto.service';
export declare class FinancialCryptoService {
    private readonly legacyCryptoService;
    constructor(legacyCryptoService: LegacyCryptoService);
    encryptValue(value: number | string, empresaId: number): string;
    decryptValueAsNumber(encryptedValue: string, empresaId: number): number;
    decryptValueAsString(encryptedValue: string, empresaId: number): string;
    isEncrypted(value: string): boolean;
    encryptMultipleValues(values: Record<string, number | string>, empresaId: number): Record<string, string>;
    decryptMultipleValues(encryptedValues: Record<string, string>, empresaId: number): Record<string, number>;
    validateEncryption(value: number | string, empresaId: number): boolean;
}
