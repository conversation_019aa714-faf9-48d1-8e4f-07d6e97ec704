"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CategoriasLancamentoModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const categoria_lancamento_entity_1 = require("../entities/categoria-lancamento.entity");
const categorias_lancamento_controller_1 = require("./categorias-lancamento.controller");
const categorias_lancamento_service_1 = require("./categorias-lancamento.service");
let CategoriasLancamentoModule = class CategoriasLancamentoModule {
};
exports.CategoriasLancamentoModule = CategoriasLancamentoModule;
exports.CategoriasLancamentoModule = CategoriasLancamentoModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([categoria_lancamento_entity_1.CategoriaLancamento])],
        controllers: [categorias_lancamento_controller_1.CategoriasLancamentoController],
        providers: [categorias_lancamento_service_1.CategoriasLancamentoService],
        exports: [categorias_lancamento_service_1.CategoriasLancamentoService],
    })
], CategoriasLancamentoModule);
//# sourceMappingURL=categorias-lancamento.module.js.map