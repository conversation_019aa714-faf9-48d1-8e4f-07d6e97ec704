import { Repository } from 'typeorm';
import { Conta } from '../entities/conta.entity';
import { CreateContaDto } from './dto/create-conta.dto';
import { UpdateContaDto } from './dto/update-conta.dto';
import { FilterContasDto } from './dto/filter-contas.dto';
import { ContaResponseDto } from './dto/conta-response.dto';
import { ContaDropdownDto } from '../lancamentos-financeiros/dto';
export declare class ContasService {
    private contaRepository;
    constructor(contaRepository: Repository<Conta>);
    create(createContaDto: CreateContaDto, empresaId: number, usuarioEmail: string): Promise<ContaResponseDto>;
    findAll(empresaId: number, filterDto?: FilterContasDto): Promise<ContaResponseDto[]>;
    findOne(id: number, empresaId: number): Promise<ContaResponseDto>;
    update(id: number, updateContaDto: UpdateContaDto, empresaId: number, usuarioEmail: string): Promise<ContaResponseDto>;
    remove(id: number, empresaId: number, usuarioEmail: string): Promise<void>;
    private mapToResponseDto;
    findAllForDropdown(empresaId: number): Promise<ContaDropdownDto[]>;
}
