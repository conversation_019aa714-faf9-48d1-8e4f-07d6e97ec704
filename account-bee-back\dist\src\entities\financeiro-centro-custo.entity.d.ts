import { CentroCusto } from './centro-custo.entity';
import { LancamentoFinanceiro } from './lancamento-financeiro.entity';
export declare class FinanceiroCentroCusto {
    id: number;
    valor: string;
    porcentagem?: number;
    centroCustoId: number;
    lancamentoFinanceiroId: number;
    centroCusto: CentroCusto;
    lancamentoFinanceiro: LancamentoFinanceiro;
    dataHoraUsuarioAlt: Date;
    dataHoraUsuarioDel?: Date;
    dataHoraUsuarioInc: Date;
    dataSync?: Date;
    isExcluido: string;
    usuarioAlt: string;
    usuarioDel?: string;
    usuarioInc: string;
    uuid?: string;
}
