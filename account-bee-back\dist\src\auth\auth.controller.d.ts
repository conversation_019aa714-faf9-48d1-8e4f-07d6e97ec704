import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { LoginResponseDto } from './dto/login-response.dto';
import { VerifyCodeDto } from './dto/verify-code.dto';
import { UserRegistrationDto } from './dto/user-registration.dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    login(loginDto: LoginDto): Promise<LoginResponseDto>;
    verificaCodLogin(verifyCodeDto: VerifyCodeDto): Promise<LoginResponseDto>;
    register(registrationDto: UserRegistrationDto): Promise<LoginResponseDto>;
}
