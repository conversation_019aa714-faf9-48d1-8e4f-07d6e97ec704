"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateFornecedorDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateFornecedorDto {
    descricao;
    cpf;
    cnpj;
    inscricaoEstadual;
    inscricaoMunicipal;
    prazoEntrega;
    prazoPagamento;
    formaPagamento;
    enderecoId;
    planoContaId;
    endereco;
    contatos;
    pessoas;
}
exports.CreateFornecedorDto = CreateFornecedorDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Descrição/nome do fornecedor',
        example: 'João Silva Fornecedor LTDA',
        maxLength: 200,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 200),
    __metadata("design:type", String)
], CreateFornecedorDto.prototype, "descricao", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'CPF do fornecedor (apenas números)',
        example: '12345678901',
        required: false,
        maxLength: 50,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(11, 50),
    __metadata("design:type", String)
], CreateFornecedorDto.prototype, "cpf", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'CNPJ do fornecedor (apenas números)',
        example: '12345678000195',
        required: false,
        maxLength: 50,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(14, 50),
    __metadata("design:type", String)
], CreateFornecedorDto.prototype, "cnpj", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Inscrição Estadual do fornecedor',
        example: '12345678',
        required: false,
        maxLength: 255,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 255),
    __metadata("design:type", String)
], CreateFornecedorDto.prototype, "inscricaoEstadual", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Inscrição Municipal do fornecedor',
        example: '987654',
        required: false,
        maxLength: 255,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 255),
    __metadata("design:type", String)
], CreateFornecedorDto.prototype, "inscricaoMunicipal", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Prazo de entrega em dias',
        example: 30,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(999),
    __metadata("design:type", Number)
], CreateFornecedorDto.prototype, "prazoEntrega", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Prazo de pagamento',
        example: '30 dias',
        required: false,
        maxLength: 200,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 200),
    __metadata("design:type", String)
], CreateFornecedorDto.prototype, "prazoPagamento", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Forma de pagamento',
        example: 'Boleto bancário',
        required: false,
        maxLength: 200,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 200),
    __metadata("design:type", String)
], CreateFornecedorDto.prototype, "formaPagamento", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID do endereço',
        example: 1,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CreateFornecedorDto.prototype, "enderecoId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID do plano de conta',
        example: 1,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CreateFornecedorDto.prototype, "planoContaId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Dados de endereço',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateFornecedorDto.prototype, "endereco", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Lista de contatos',
        required: false,
        isArray: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CreateFornecedorDto.prototype, "contatos", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Lista de pessoas',
        required: false,
        isArray: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CreateFornecedorDto.prototype, "pessoas", void 0);
//# sourceMappingURL=create-fornecedor.dto.js.map