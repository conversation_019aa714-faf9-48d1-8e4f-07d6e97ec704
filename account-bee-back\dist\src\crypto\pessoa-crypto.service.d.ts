export declare class PessoaCryptoService {
    private readonly PADDING_CHARACTERS;
    private readonly ALGORITHM;
    private doKey;
    encrypt(strToEncrypt: string, publicKey: number): string | null;
    decrypt(strToDecrypt: string, publicKey: number): string | null;
    encryptCpf(cpf: string, empresaId: number): string | null;
    encryptCnpj(cnpj: string, empresaId: number): string | null;
    encryptEmail(email: string, empresaId: number): string | null;
}
