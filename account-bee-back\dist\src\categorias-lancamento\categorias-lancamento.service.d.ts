import { Repository } from 'typeorm';
import { CategoriaLancamento } from '../entities/categoria-lancamento.entity';
import { CategoriaLancamentoDropdownDto } from '../lancamentos-financeiros/dto';
export declare class CategoriasLancamentoService {
    private readonly categoriaRepository;
    constructor(categoriaRepository: Repository<CategoriaLancamento>);
    findAllByEmpresa(empresaId: number): Promise<CategoriaLancamentoDropdownDto[]>;
    findById(id: number, empresaId: number): Promise<CategoriaLancamento | null>;
}
