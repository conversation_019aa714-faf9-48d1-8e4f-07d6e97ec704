"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaginatedFornecedorResponseDto = exports.FornecedorResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class FornecedorResponseDto {
    id;
    uuid;
    descricao;
    cpf;
    cnpj;
    inscricaoEstadual;
    inscricaoMunicipal;
    prazoEntrega;
    prazoPagamento;
    formaPagamento;
    empresaId;
    enderecoId;
    planoContaId;
    isExcluido;
    dataHoraUsuarioInc;
    dataHoraUsuarioAlt;
    dataHoraUsuarioDel;
    usuarioInc;
    usuarioAlt;
    usuarioDel;
    dataSync;
}
exports.FornecedorResponseDto = FornecedorResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID único do fornecedor',
        example: 1,
    }),
    __metadata("design:type", Number)
], FornecedorResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'UUID do fornecedor',
        example: 'a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6',
    }),
    __metadata("design:type", String)
], FornecedorResponseDto.prototype, "uuid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Descrição/nome do fornecedor',
        example: 'João Silva Fornecedor LTDA',
    }),
    __metadata("design:type", String)
], FornecedorResponseDto.prototype, "descricao", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'CPF do fornecedor',
        example: '12345678901',
        required: false,
    }),
    __metadata("design:type", String)
], FornecedorResponseDto.prototype, "cpf", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'CNPJ do fornecedor',
        example: '12345678000195',
        required: false,
    }),
    __metadata("design:type", String)
], FornecedorResponseDto.prototype, "cnpj", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Inscrição Estadual do fornecedor',
        example: '12345678',
        required: false,
    }),
    __metadata("design:type", String)
], FornecedorResponseDto.prototype, "inscricaoEstadual", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Inscrição Municipal do fornecedor',
        example: '987654',
        required: false,
    }),
    __metadata("design:type", String)
], FornecedorResponseDto.prototype, "inscricaoMunicipal", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Prazo de entrega em dias',
        example: 30,
        required: false,
    }),
    __metadata("design:type", Number)
], FornecedorResponseDto.prototype, "prazoEntrega", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Prazo de pagamento',
        example: '30 dias',
        required: false,
    }),
    __metadata("design:type", String)
], FornecedorResponseDto.prototype, "prazoPagamento", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Forma de pagamento',
        example: 'Boleto bancário',
        required: false,
    }),
    __metadata("design:type", String)
], FornecedorResponseDto.prototype, "formaPagamento", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da empresa',
        example: 1,
    }),
    __metadata("design:type", Number)
], FornecedorResponseDto.prototype, "empresaId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID do endereço',
        example: 1,
        required: false,
    }),
    __metadata("design:type", Number)
], FornecedorResponseDto.prototype, "enderecoId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID do plano de conta',
        example: 1,
        required: false,
    }),
    __metadata("design:type", Number)
], FornecedorResponseDto.prototype, "planoContaId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Indica se o registro foi excluído logicamente',
        example: 'N',
    }),
    __metadata("design:type", String)
], FornecedorResponseDto.prototype, "isExcluido", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data e hora de criação do registro',
        example: '2024-01-01T10:00:00.000Z',
    }),
    __metadata("design:type", Date)
], FornecedorResponseDto.prototype, "dataHoraUsuarioInc", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data e hora da última alteração',
        example: '2024-01-01T15:30:00.000Z',
    }),
    __metadata("design:type", Date)
], FornecedorResponseDto.prototype, "dataHoraUsuarioAlt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data e hora da exclusão lógica',
        example: '2024-01-01T18:00:00.000Z',
        required: false,
    }),
    __metadata("design:type", Date)
], FornecedorResponseDto.prototype, "dataHoraUsuarioDel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Usuário que criou o registro',
        example: '<EMAIL>',
    }),
    __metadata("design:type", String)
], FornecedorResponseDto.prototype, "usuarioInc", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Usuário que fez a última alteração',
        example: '<EMAIL>',
    }),
    __metadata("design:type", String)
], FornecedorResponseDto.prototype, "usuarioAlt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Usuário que fez a exclusão lógica',
        example: '<EMAIL>',
        required: false,
    }),
    __metadata("design:type", String)
], FornecedorResponseDto.prototype, "usuarioDel", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de sincronização',
        example: '2024-01-01T20:00:00.000Z',
        required: false,
    }),
    __metadata("design:type", Date)
], FornecedorResponseDto.prototype, "dataSync", void 0);
class PaginatedFornecedorResponseDto {
    data;
    total;
    page;
    limit;
    totalPages;
}
exports.PaginatedFornecedorResponseDto = PaginatedFornecedorResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Lista de fornecedores',
        type: [FornecedorResponseDto],
    }),
    __metadata("design:type", Array)
], PaginatedFornecedorResponseDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total de registros encontrados',
        example: 100,
    }),
    __metadata("design:type", Number)
], PaginatedFornecedorResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Página atual',
        example: 1,
    }),
    __metadata("design:type", Number)
], PaginatedFornecedorResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Limite de registros por página',
        example: 10,
    }),
    __metadata("design:type", Number)
], PaginatedFornecedorResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total de páginas',
        example: 10,
    }),
    __metadata("design:type", Number)
], PaginatedFornecedorResponseDto.prototype, "totalPages", void 0);
//# sourceMappingURL=fornecedor-response.dto.js.map