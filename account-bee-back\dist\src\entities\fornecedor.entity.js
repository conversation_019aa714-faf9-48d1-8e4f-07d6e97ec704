"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Fornecedor = void 0;
const typeorm_1 = require("typeorm");
const empresa_entity_1 = require("./empresa.entity");
let Fornecedor = class Fornecedor {
    id;
    dataHoraUsuarioAlt;
    dataHoraUsuarioDel;
    dataHoraUsuarioInc;
    dataSync;
    isExcluido;
    usuarioAlt;
    usuarioDel;
    usuarioInc;
    uuid;
    descricao;
    cpf;
    cnpj;
    inscricaoEstadual;
    inscricaoMunicipal;
    prazoEntrega;
    prazoPagamento;
    formaPagamento;
    empresaId;
    empresa;
    enderecoId;
    planoContaId;
};
exports.Fornecedor = Fornecedor;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'ID' }),
    __metadata("design:type", Number)
], Fornecedor.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_HORA_USUARIO_ALT', type: 'datetime' }),
    __metadata("design:type", Date)
], Fornecedor.prototype, "dataHoraUsuarioAlt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_HORA_USUARIO_DEL', type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], Fornecedor.prototype, "dataHoraUsuarioDel", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_HORA_USUARIO_INC', type: 'datetime' }),
    __metadata("design:type", Date)
], Fornecedor.prototype, "dataHoraUsuarioInc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_SYNC', type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], Fornecedor.prototype, "dataSync", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_EXCLUIDO', length: 1, default: 'N' }),
    __metadata("design:type", String)
], Fornecedor.prototype, "isExcluido", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_ALT', length: 500 }),
    __metadata("design:type", String)
], Fornecedor.prototype, "usuarioAlt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_DEL', length: 500, nullable: true }),
    __metadata("design:type", String)
], Fornecedor.prototype, "usuarioDel", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_INC', length: 500 }),
    __metadata("design:type", String)
], Fornecedor.prototype, "usuarioInc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'UUID', length: 50, nullable: true }),
    __metadata("design:type", String)
], Fornecedor.prototype, "uuid", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DESCRICAO', length: 200 }),
    __metadata("design:type", String)
], Fornecedor.prototype, "descricao", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CPF', length: 50, nullable: true }),
    __metadata("design:type", String)
], Fornecedor.prototype, "cpf", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CNPJ', length: 50, nullable: true }),
    __metadata("design:type", String)
], Fornecedor.prototype, "cnpj", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'INSCRICAO_ESTADUAL', length: 255, nullable: true }),
    __metadata("design:type", String)
], Fornecedor.prototype, "inscricaoEstadual", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'INSCRICAO_MUNICIPAL', length: 255, nullable: true }),
    __metadata("design:type", String)
], Fornecedor.prototype, "inscricaoMunicipal", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'PRAZO_ENTREGA', nullable: true }),
    __metadata("design:type", Number)
], Fornecedor.prototype, "prazoEntrega", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'PRAZO_PAGAMENTO', length: 200, nullable: true }),
    __metadata("design:type", String)
], Fornecedor.prototype, "prazoPagamento", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'FORMA_PAGAMENTO', length: 200, nullable: true }),
    __metadata("design:type", String)
], Fornecedor.prototype, "formaPagamento", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'EMPRESA_ID' }),
    __metadata("design:type", Number)
], Fornecedor.prototype, "empresaId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => empresa_entity_1.Empresa),
    (0, typeorm_1.JoinColumn)({ name: 'EMPRESA_ID' }),
    __metadata("design:type", empresa_entity_1.Empresa)
], Fornecedor.prototype, "empresa", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ENDERECO_ID', nullable: true }),
    __metadata("design:type", Number)
], Fornecedor.prototype, "enderecoId", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'PLANO_CONTA_ID', nullable: true }),
    __metadata("design:type", Number)
], Fornecedor.prototype, "planoContaId", void 0);
exports.Fornecedor = Fornecedor = __decorate([
    (0, typeorm_1.Entity)({ name: 'fornecedor' })
], Fornecedor);
//# sourceMappingURL=fornecedor.entity.js.map