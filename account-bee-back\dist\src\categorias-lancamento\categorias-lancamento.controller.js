"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CategoriasLancamentoController = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const categorias_lancamento_service_1 = require("./categorias-lancamento.service");
let CategoriasLancamentoController = class CategoriasLancamentoController {
    categoriasService;
    constructor(categoriasService) {
        this.categoriasService = categoriasService;
    }
    async getDropdownOptions(req) {
        const empresaId = req.user.empresaId;
        return this.categoriasService.findAllByEmpresa(empresaId);
    }
};
exports.CategoriasLancamentoController = CategoriasLancamentoController;
__decorate([
    (0, common_1.Get)('dropdown'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CategoriasLancamentoController.prototype, "getDropdownOptions", null);
exports.CategoriasLancamentoController = CategoriasLancamentoController = __decorate([
    (0, common_1.Controller)('categorias-lancamento'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [categorias_lancamento_service_1.CategoriasLancamentoService])
], CategoriasLancamentoController);
//# sourceMappingURL=categorias-lancamento.controller.js.map