export declare enum UserRole {
    ADMIN_GERAL = "admin_geral",
    ADMIN_LOCAL = "admin_local",
    OPERADOR = "operador",
    OPERADOR_ESTOQUE = "operador_estoque"
}
export declare enum UserPermission {
    USERS_CREATE = "users.create",
    USERS_READ = "users.read",
    USERS_UPDATE = "users.update",
    USERS_DELETE = "users.delete",
    USERS_MANAGE = "users.manage",
    FINANCEIRO_VIEW_SALDOS = "financeiro.view_saldos",
    FINANCEIRO_MANAGE_LANCAMENTOS = "financeiro.manage_lancamentos",
    FINANCEIRO_REPORTS = "financeiro.reports",
    PRODUTOS_MANAGE = "produtos.manage",
    FISCAL_ACCESS = "fiscal.access",
    CONFIG_ACCESS = "config.access",
    COMANDAS_REPORTS = "comandas.reports"
}
