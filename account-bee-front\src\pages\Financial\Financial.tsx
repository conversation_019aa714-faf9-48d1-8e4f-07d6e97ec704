import { useState, useEffect } from 'react';
import { Plus, Edit, Check, Calendar, AlertTriangle, ChevronLeft, ChevronRight, X, Filter, RefreshCw } from 'lucide-react';
import { Layout } from '@/components/layout';
import { Button, BulkActionsToolbar } from '@/components/ui/forms';
import { Card } from '@/components/ui/layout';
import { Badge } from '@/components/ui/shadcn/badge';
import { Checkbox } from '@/components/ui/forms';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/shadcn/tabs';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/shadcn/table';
import { TablePagination } from '@/components/ui/navigation/TablePagination/TablePagination';
import { useAccountsPayable } from '@/hooks/useAccountsPayable';
import { useAccountsReceivable } from '@/hooks/useAccountsReceivable';
import { useFinancialData } from '@/hooks/useFinancialData';
import { AccountPayableFormDialog } from '@/pages/AccountsPayable/components/AccountPayableFormDialog';
import { AccountReceivableFormDialog } from '@/pages/AccountsReceivable/components/AccountReceivableFormDialog';
import { ReceitaFormDialog } from './components/ReceitaFormDialog';
import { DespesaFormDialog } from './components/DespesaFormDialog';
import { ConfirmDialog } from '@/components/ui/feedback';
import { useToast } from '@/hooks/use-toast';
import { AccountPayable, AccountReceivable } from '@/types/accounts';
import { AdvancedFiltersModal, AdvancedFiltersData } from '@/components/ui/forms/AdvancedFiltersModal';
import { exportToExcel } from '@/utils/exportUtils';

const MONTHS = [
  'Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',
  'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'
];

export default function Financial() {
  // Hooks para dados mockados (manter para compatibilidade)
  const { accounts: payableAccounts, loading: payableLoading, markAsPaid, markAsUnpaid, markMultipleAsPaid, updateMultipleStatus: updateMultiplePayableStatus, deleteMultipleAccounts: deleteMultiplePayableAccounts } = useAccountsPayable();
  const { accounts: receivableAccounts, loading: receivableLoading, markAsReceived, markAsNotReceived, updateMultipleStatus: updateMultipleReceivableStatus, deleteMultipleAccounts: deleteMultipleReceivableAccounts } = useAccountsReceivable();

  // Hook para dados reais do banco
  const {
    transactions,
    summary,
    loading: financialLoading,
    receitasLoaded,
    despesasLoaded,
    currentType,
    fetchReceitas,
    fetchDespesas,
    refreshData,
    clearCache
  } = useFinancialData();

  const { toast } = useToast();

  const [activeTab, setActiveTab] = useState<'pagar' | 'receber'>('pagar');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [advancedFilters, setAdvancedFilters] = useState<AdvancedFiltersData>({});
  const [isAdvancedFiltersOpen, setIsAdvancedFiltersOpen] = useState(false);
  
  // Modal states
  const [isCreatePayableOpen, setIsCreatePayableOpen] = useState(false);
  const [isCreateReceitaOpen, setIsCreateReceitaOpen] = useState(false);
  const [isCreateDespesaOpen, setIsCreateDespesaOpen] = useState(false);
  const [editingPayable, setEditingPayable] = useState<AccountPayable | null>(null);
  const [editingReceivable, setEditingReceivable] = useState<AccountReceivable | null>(null);

  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();

  // Navigation functions
  const goToPreviousMonth = () => {
    setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() - 1, 1));
  };

  const goToNextMonth = () => {
    setCurrentDate(prev => new Date(prev.getFullYear(), prev.getMonth() + 1, 1));
  };

  // Filter accounts by month and advanced filters
  const filterAccounts = (accountsList: any[]) => {
    return accountsList.filter(account => {
      const accountDate = new Date(account.dueDate);
      
      // Month filter
      if (accountDate.getMonth() !== currentMonth || accountDate.getFullYear() !== currentYear) {
        return false;
      }
      
      // Advanced filters
      if (advancedFilters.category && account.category?.id !== advancedFilters.category) {
        return false;
      }
      
      if (advancedFilters.location && account.location !== advancedFilters.location) {
        return false;
      }
      
      if (advancedFilters.status && account.status !== advancedFilters.status) {
        return false;
      }
      
      if (advancedFilters.account && account.account?.id !== advancedFilters.account) {
        return false;
      }
      
      if (advancedFilters.supplier) {
        const supplierField = activeTab === 'pagar' ? account.supplier : account.client;
        if (!supplierField?.toLowerCase().includes(advancedFilters.supplier.toLowerCase())) {
          return false;
        }
      }
      
      if (advancedFilters.description) {
        if (!account.description?.toLowerCase().includes(advancedFilters.description.toLowerCase())) {
          return false;
        }
      }
      
      const accountAmount = activeTab === 'pagar' ? account.amount : (account.netAmount || account.amount);
      
      if (advancedFilters.minAmount && accountAmount < advancedFilters.minAmount) {
        return false;
      }
      
      if (advancedFilters.maxAmount && accountAmount > advancedFilters.maxAmount) {
        return false;
      }
      
      return true;
    });
  };

  // Converter dados reais para formato da tabela
  const convertTransactionsToTableFormat = (transactions: any[]) => {
    return transactions.map(transaction => ({
      id: transaction.id.toString(),
      description: transaction.descricao,
      dueDate: transaction.dataLancamento || transaction.dataCompetencia,
      amount: transaction.valor,
      client: transaction.pessoa?.nome || 'N/A',
      supplier: transaction.pessoa?.nome || 'N/A',
      location: transaction.local?.nome || '',
      status: transaction.efetivado ? (activeTab === 'pagar' ? 'paid' : 'received') : 'open',
      category: transaction.categoriaLctoFinanceiro?.descricao || '',
      account: transaction.conta?.descricao || '',
      observation: transaction.observacao || '',
    }));
  };

  // Usar dados reais se disponíveis, senão usar dados mockados
  const realPayableAccounts = activeTab === 'pagar' ? convertTransactionsToTableFormat(transactions) : [];
  const realReceivableAccounts = activeTab === 'receber' ? convertTransactionsToTableFormat(transactions) : [];

  const filteredPayableAccounts = filterAccounts(realPayableAccounts.length > 0 ? realPayableAccounts : payableAccounts);
  const filteredReceivableAccounts = filterAccounts(realReceivableAccounts.length > 0 ? realReceivableAccounts : receivableAccounts);

  // Função para carregar dados apenas quando necessário
  const loadDataForActiveTab = async () => {
    try {
      if (activeTab === 'pagar' && !despesasLoaded) {
        console.log('🔄 Carregando despesas pela primeira vez...');
        await fetchDespesas({ page: 1, limit: 100 });
      } else if (activeTab === 'receber' && !receitasLoaded) {
        console.log('🔄 Carregando receitas pela primeira vez...');
        await fetchReceitas({ page: 1, limit: 100 });
      } else {
        console.log('📊 Dados já carregados para a aba:', activeTab);
      }
    } catch (error) {
      console.error('❌ Erro ao carregar dados financeiros:', error);
      toast({
        title: "Erro ao carregar dados",
        description: error instanceof Error ? error.message : "Erro desconhecido ao carregar dados financeiros",
        variant: "destructive",
      });
    }
  };

  // Carregar dados apenas quando a aba mudar E os dados não estiverem carregados
  useEffect(() => {
    loadDataForActiveTab();
  }, [activeTab]); // Removido fetchDespesas e fetchReceitas das dependências

  // Reset page when tab, month, filters, or page size change
  useEffect(() => {
    setCurrentPage(1);
  }, [activeTab, currentMonth, currentYear, advancedFilters, pageSize]);

  // Pagination
  const getCurrentPageData = (data: any[]) => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return data.slice(startIndex, endIndex);
  };

  const getTotalPages = (data: any[]) => Math.ceil(data.length / pageSize);

  // Calculate totals
  const calculateTotals = (accountsList: any[], type: 'payable' | 'receivable') => {
    const paid = accountsList.filter(acc => acc.status === (type === 'payable' ? 'paid' : 'received'));
    const open = accountsList.filter(acc => acc.status === 'open');
    const overdue = accountsList.filter(acc => acc.status === 'overdue');
    const selected = accountsList.filter(acc => selectedIds.includes(acc.id));

    const getAmount = (acc: any) => type === 'payable' ? acc.amount : (acc.netAmount || acc.amount);

    return {
      selected: selected.reduce((sum, acc) => sum + getAmount(acc), 0),
      paid: paid.reduce((sum, acc) => sum + getAmount(acc), 0),
      open: open.reduce((sum, acc) => sum + getAmount(acc), 0),
      overdue: overdue.reduce((sum, acc) => sum + getAmount(acc), 0),
      total: accountsList.reduce((sum, acc) => sum + getAmount(acc), 0),
    };
  };

  const currentData = activeTab === 'pagar' ? filteredPayableAccounts : filteredReceivableAccounts;
  const paginatedData = getCurrentPageData(currentData);
  const totalPages = getTotalPages(currentData);
  const totals = calculateTotals(currentData, activeTab === 'pagar' ? 'payable' : 'receivable');

  // Utility functions
  const formatCurrency = (value: number) => {
    return value.toLocaleString('pt-BR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', { 
      day: '2-digit', 
      month: '2-digit' 
    });
  };

  const getStatusBadge = (account: any, type: 'payable' | 'receivable') => {
    const statusLabel = type === 'payable' ? 'Pago' : 'Recebido';
    const statusValue = type === 'payable' ? 'paid' : 'received';
    
    if (account.status === statusValue) {
      return (
        <Badge variant="default" className="bg-green-100 text-green-800 text-xs px-2 py-0">
          <Check className="w-3 h-3 mr-1" />
          {statusLabel}
        </Badge>
      );
    }

    if (account.status === 'overdue') {
      return (
        <Badge variant="destructive" className="text-xs px-2 py-0">
          <AlertTriangle className="w-3 h-3 mr-1" />
          Vencido
        </Badge>
      );
    }

    return (
      <Badge variant="secondary" className="text-xs px-2 py-0">
        <Calendar className="w-3 h-3 mr-1" />
        Em Aberto
      </Badge>
    );
  };

  const handleSelectItem = (id: string) => {
    setSelectedIds(prev => 
      prev.includes(id) 
        ? prev.filter(i => i !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedIds.length === paginatedData.length && paginatedData.length > 0) {
      setSelectedIds([]);
    } else {
      setSelectedIds(paginatedData.map(item => item.id));
    }
  };

  const handleMarkAsPaid = async (id: string) => {
    try {
      if (activeTab === 'pagar') {
        await markAsPaid(id);
      } else {
        await markAsReceived(id);
      }
      toast({
        title: 'Status atualizado',
        description: `Item marcado como ${activeTab === 'pagar' ? 'pago' : 'recebido'}.`,
      });
    } catch (error) {
      toast({
        title: 'Erro',
        description: 'Não foi possível atualizar o status.',
        variant: 'destructive',
      });
    }
  };

  const handleMarkAsUnpaid = async (id: string) => {
    try {
      if (activeTab === 'pagar') {
        await markAsUnpaid(id);
      } else {
        await markAsNotReceived(id);
      }
      toast({
        title: 'Status atualizado',
        description: `Item marcado como não ${activeTab === 'pagar' ? 'pago' : 'recebido'}.`,
      });
    } catch (error) {
      toast({
        title: 'Erro',
        description: 'Não foi possível atualizar o status.',
        variant: 'destructive',
      });
    }
  };

  const handleMarkMultipleAsPaid = async () => {
    try {
      if (activeTab === 'pagar') {
        await markMultipleAsPaid(selectedIds);
      } else {
        // Implementar markMultipleAsReceived se necessário
        for (const id of selectedIds) {
          await markAsReceived(id);
        }
      }
      setSelectedIds([]);
      toast({
        title: 'Status atualizado',
        description: `${selectedIds.length} item(s) marcado(s) como ${activeTab === 'pagar' ? 'pago(s)' : 'recebido(s)'}.`,
      });
    } catch (error) {
      toast({
        title: 'Erro',
        description: 'Não foi possível atualizar os status.',
        variant: 'destructive',
      });
    }
  };

  const handleStatusChange = async (status: 'open' | 'paid' | 'received' | 'overdue') => {
    try {
      if (activeTab === 'pagar') {
        await updateMultiplePayableStatus(selectedIds, status as 'open' | 'paid' | 'overdue');
      } else {
        await updateMultipleReceivableStatus(selectedIds, status as 'open' | 'received' | 'overdue');
      }
      setSelectedIds([]);
      
      const statusLabels = {
        'open': 'Em Aberto',
        'paid': 'Pago',
        'received': 'Recebido',
        'overdue': 'Vencido'
      };

      toast({
        title: 'Status atualizado',
        description: `${selectedIds.length} item(s) marcado(s) como ${statusLabels[status]}.`,
      });
    } catch (error) {
      toast({
        title: 'Erro',
        description: 'Não foi possível atualizar os status.',
        variant: 'destructive',
      });
    }
  };

  const handleBulkDelete = async () => {
    try {
      if (activeTab === 'pagar') {
        await deleteMultiplePayableAccounts(selectedIds);
      } else {
        await deleteMultipleReceivableAccounts(selectedIds);
      }
      setSelectedIds([]);
      
      toast({
        title: 'Itens excluídos',
        description: `${selectedIds.length} item(s) excluído(s) com sucesso.`,
      });
    } catch (error) {
      toast({
        title: 'Erro',
        description: 'Não foi possível excluir os itens.',
        variant: 'destructive',
      });
    }
  };

  const prepareExportData = (accounts: any[]) => {
    return accounts.map(account => ({
      Data: formatDate(account.dueDate),
      Descrição: account.description,
      [activeTab === 'pagar' ? 'Fornecedor' : 'Cliente']: activeTab === 'pagar' ? account.supplier : account.client,
      Local: account.location || '',
      Conta: account.account?.name || '',
      Valor: activeTab === 'pagar' ? account.amount : (account.netAmount || account.amount),
      Status: account.status === (activeTab === 'pagar' ? 'paid' : 'received') ? 
        (activeTab === 'pagar' ? 'Pago' : 'Recebido') : 
        account.status === 'overdue' ? 'Vencido' : 'Em Aberto',
      Categoria: account.category?.name || '',
    }));
  };

  const handleExport = () => {
    // Se há itens selecionados, exporta apenas os selecionados. Caso contrário, exporta todos
    const accountsToExport = selectedIds.length > 0 
      ? currentData.filter(account => selectedIds.includes(account.id))
      : currentData;
    
    const exportData = prepareExportData(accountsToExport);
    const filename = selectedIds.length > 0 
      ? `${activeTab === 'pagar' ? 'contas-a-pagar' : 'contas-a-receber'}-selecionadas-${MONTHS[currentMonth]}-${currentYear}`
      : `${activeTab === 'pagar' ? 'contas-a-pagar' : 'contas-a-receber'}-${MONTHS[currentMonth]}-${currentYear}`;
    
    if (exportToExcel(exportData, filename, activeTab === 'pagar' ? 'Contas a Pagar' : 'Contas a Receber')) {
      const description = selectedIds.length > 0 
        ? `${accountsToExport.length} item(s) selecionado(s) exportado(s) para Excel.`
        : `${accountsToExport.length} item(s) exportado(s) para Excel.`;
        
      toast({
        title: 'Exportação concluída',
        description,
      });
    } else {
      toast({
        title: 'Erro na exportação',
        description: 'Não foi possível exportar os dados.',
        variant: 'destructive',
      });
    }
  };



  const handleApplyFilters = (filters: AdvancedFiltersData) => {
    setAdvancedFilters(filters);
    setIsAdvancedFiltersOpen(false);
    // Recarregar dados com novos filtros
    refreshData(activeTab === 'pagar' ? 'despesas' : 'receitas', filters);
  };

  const handleClearFilters = () => {
    setAdvancedFilters({});
    setIsAdvancedFiltersOpen(false);
    // Recarregar dados sem filtros
    refreshData(activeTab === 'pagar' ? 'despesas' : 'receitas');
  };

  // Função para forçar atualização dos dados
  const handleRefreshData = async () => {
    console.log('🔄 Usuário solicitou atualização dos dados...');
    await refreshData(activeTab === 'pagar' ? 'despesas' : 'receitas');
    toast({
      title: "Dados atualizados",
      description: `${activeTab === 'pagar' ? 'Despesas' : 'Receitas'} atualizadas com sucesso.`,
    });
  };

  const hasActiveFilters = Object.keys(advancedFilters).some(key => 
    advancedFilters[key as keyof AdvancedFiltersData] !== undefined
  );

  return (
    <Layout>
      <div className="h-screen flex flex-col overflow-hidden">
        {/* Compact Header */}
        <div className="flex items-center justify-between px-6 py-3 border-b">
          <div className="flex items-center gap-4">
            <h1 className="text-2xl font-bold">Financeiro</h1>
          </div>

          {/* Month Navigation */}
          <div className="flex items-center gap-3">
            <Button variant="outline" size="sm" onClick={goToPreviousMonth}>
              <ChevronLeft className="w-4 h-4" />
            </Button>
            <div className="text-lg font-semibold min-w-[100px] text-center">
              {MONTHS[currentMonth]}/{currentYear}
            </div>
            <Button variant="outline" size="sm" onClick={goToNextMonth}>
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            <Button 
              variant={hasActiveFilters ? "default" : "outline"} 
              size="sm" 
              className="gap-2"
              onClick={() => setIsAdvancedFiltersOpen(true)}
            >
              <Filter className="w-4 h-4" />
              Filtros Avançados
              {hasActiveFilters && (
                <span className="bg-white text-blue-600 text-xs rounded-full px-1.5 py-0.5 ml-1">
                  {Object.keys(advancedFilters).length}
                </span>
              )}
            </Button>
            <BulkActionsToolbar
              type={activeTab === 'pagar' ? 'payable' : 'receivable'}
              selectedCount={selectedIds.length}
              selectedData={currentData.filter(account => selectedIds.includes(account.id))}
              onStatusChange={handleStatusChange}
              onBulkDelete={handleBulkDelete}
              onExport={handleExport}
              disabled={payableLoading || receivableLoading}
            />
          </div>
        </div>

        {/* Compact Tabs */}
        <div className="px-6 py-2 border-b">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'pagar' | 'receber')}>
            <div className="flex items-center justify-between">
              <TabsList className="grid w-auto grid-cols-2">
                <TabsTrigger value="pagar" className="text-sm">Pagar</TabsTrigger>
                <TabsTrigger value="receber" className="text-sm">Receber</TabsTrigger>
              </TabsList>
              
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleRefreshData}
                  disabled={financialLoading}
                  className="gap-2"
                >
                  <RefreshCw className={`w-4 h-4 ${financialLoading ? 'animate-spin' : ''}`} />
                  Atualizar
                </Button>

                <Button
                  size="sm"
                  onClick={() => activeTab === 'pagar' ? setIsCreateDespesaOpen(true) : setIsCreateReceitaOpen(true)}
                  className="gap-2"
                >
                  <Plus className="w-4 h-4" />
                  Nova {activeTab === 'pagar' ? 'Despesa' : 'Receita'}
                </Button>
              </div>
            </div>
          </Tabs>
        </div>

        {/* Table Container */}
        <div className="flex-1 flex flex-col px-6 py-2 overflow-hidden">
          <div className="rounded-md border flex-1 overflow-auto">
            <Table>
              <TableHeader className="sticky top-0 bg-background">
                <TableRow className="h-10">
                  <TableHead className="w-10">
                    <Checkbox 
                      checked={selectedIds.length === paginatedData.length && paginatedData.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </TableHead>
                  <TableHead className="text-xs">Data</TableHead>
                  <TableHead className="text-xs">Descrição</TableHead>
                  <TableHead className="text-xs">{activeTab === 'pagar' ? 'Fornecedor' : 'Cliente'}</TableHead>
                  <TableHead className="text-xs">Local</TableHead>
                  <TableHead className="text-xs">Conta</TableHead>
                  <TableHead className="text-xs text-right">Valor</TableHead>
                  <TableHead className="text-xs">Status</TableHead>
                  <TableHead className="text-xs w-32">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedData.map((item) => (
                  <TableRow key={item.id} className="h-12">
                    <TableCell>
                      <Checkbox 
                        checked={selectedIds.includes(item.id)}
                        onCheckedChange={() => handleSelectItem(item.id)}
                      />
                    </TableCell>
                    <TableCell className="text-sm">{formatDate(item.dueDate)}</TableCell>
                    <TableCell className="text-sm max-w-[200px] truncate" title={item.description}>
                      {item.description}
                    </TableCell>
                    <TableCell className="text-sm">
                      {activeTab === 'pagar' ? item.supplier : item.client}
                    </TableCell>
                    <TableCell className="text-sm">{item.location || '-'}</TableCell>
                    <TableCell className="text-sm">{item.account?.name || '-'}</TableCell>
                    <TableCell className="text-sm text-right font-mono">
                      {formatCurrency(activeTab === 'pagar' ? item.amount : (item.netAmount || item.amount))}
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(item, activeTab === 'pagar' ? 'payable' : 'receivable')}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        {item.status === (activeTab === 'pagar' ? 'paid' : 'received') ? (
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleMarkAsUnpaid(item.id)}
                            className="h-8 w-8 p-0"
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        ) : (
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleMarkAsPaid(item.id)}
                            className="h-8 w-8 p-0"
                          >
                            <Check className="w-4 h-4" />
                          </Button>
                        )}
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => {
                            if (activeTab === 'pagar') {
                              setEditingPayable(item);
                            } else {
                              setEditingReceivable(item);
                            }
                          }}
                          className="h-8 w-8 p-0"
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
                {/* Totals row right after the last record */}
                <TableRow>
                  <TableCell colSpan={9} className="border-t-2 bg-slate-50 p-4">
                    <div className="flex flex-wrap items-center justify-between gap-x-6 gap-y-2 text-sm">
                      <div className="flex items-center gap-2">
                        <span className="text-slate-600">Selecionadas:</span>
                        <span className="font-mono font-semibold text-slate-900">R$ {formatCurrency(totals.selected)}</span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <span className="text-slate-600">{activeTab === 'pagar' ? 'Pagas' : 'Recebidas'}:</span>
                        <span className="font-mono font-semibold text-green-600">R$ {formatCurrency(totals.paid)}</span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <span className="text-slate-600">A {activeTab === 'pagar' ? 'Pagar' : 'Receber'}:</span>
                        <span className="font-mono font-semibold text-blue-600">R$ {formatCurrency(totals.open)}</span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <span className="text-slate-600">Vencidas:</span>
                        <span className="font-mono font-semibold text-red-600">R$ {formatCurrency(totals.overdue)}</span>
                      </div>
                      
                      <div className="flex items-center gap-2 border-l border-slate-300 pl-4 ml-2">
                        <span className="text-slate-800 font-semibold">Total:</span>
                        <span className="font-mono font-bold text-slate-900">R$ {formatCurrency(totals.total)}</span>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="bg-background border border-t-0 rounded-b-lg px-4 py-3 mt-0">
              <TablePagination
                currentPage={currentPage}
                totalPages={totalPages}
                pageSize={pageSize}
                totalItems={currentData.length}
                onPageChange={setCurrentPage}
                onPageSizeChange={setPageSize}
              />
            </div>
          )}
        </div>

        {/* Modals */}
        <AccountPayableFormDialog
          open={isCreatePayableOpen}
          onOpenChange={setIsCreatePayableOpen}
          onSuccess={() => {}}
        />



        <AccountPayableFormDialog
          open={!!editingPayable}
          onOpenChange={(open) => !open && setEditingPayable(null)}
          account={editingPayable}
          onSuccess={() => {}}
        />

        <AccountReceivableFormDialog
          open={!!editingReceivable}
          onOpenChange={(open) => !open && setEditingReceivable(null)}
          account={editingReceivable}
          onSuccess={() => {}}
        />



        <ReceitaFormDialog
          open={isCreateReceitaOpen}
          onOpenChange={setIsCreateReceitaOpen}
          onSuccess={() => {
            console.log('✅ Receita criada com sucesso');
            // Forçar recarga das receitas
            refreshData('receitas');
            toast({
              title: "Receita criada",
              description: "Nova receita adicionada com sucesso.",
            });
          }}
        />

        <DespesaFormDialog
          open={isCreateDespesaOpen}
          onOpenChange={setIsCreateDespesaOpen}
          onSuccess={() => {
            console.log('✅ Despesa criada com sucesso');
            // Forçar recarga das despesas
            refreshData('despesas');
            toast({
              title: "Despesa criada",
              description: "Nova despesa adicionada com sucesso.",
            });
          }}
        />

        <AdvancedFiltersModal
          open={isAdvancedFiltersOpen}
          onOpenChange={setIsAdvancedFiltersOpen}
          onApplyFilters={handleApplyFilters}
          onClearFilters={handleClearFilters}
          currentFilters={advancedFilters}
          activeTab={activeTab}
        />
      </div>
    </Layout>
  );
}