"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Usuario = void 0;
const typeorm_1 = require("typeorm");
const empresa_entity_1 = require("./empresa.entity");
const bitTransformer = {
    to: (value) => value,
    from: (value) => (value ? !!value[0] : false),
};
let Usuario = class Usuario {
    id;
    dataHoraUsuarioAlt;
    dataHoraUsuarioDel;
    dataHoraUsuarioInc;
    dataSync;
    isExcluido;
    usuarioAlt;
    usuarioDel;
    usuarioInc;
    uuid;
    email;
    nomeUsuario;
    senha;
    empresaId;
    empresa;
    admGeral;
    admLocal;
    operador;
    operadorEstoque;
    token;
    dataExpiracaoToken;
    flTourVisualizado;
    visualizaSaldosBancarios;
    gerenciarLctosFinanceiros;
    relatoriosFinanceiros;
    relatoriosComandas;
    gerenciarProdutos;
    podeAcessarFiscal;
    podeAcessarConfiguracoes;
    podeValidarPdvs;
    idCloud;
    podePagarMensalidade;
    podeDarCortesia;
    podeLancarInventario;
    podeEditarInventario;
    dataEnvioEsqueciMinhaSenha;
    sessaoUsuarioLogado;
    sessaoUsuarioLogadoMobile;
    podeVisualizarCatFinOculto;
    podeVisualizarEmpProdFinanceiro;
    telefone;
    hashContatoNotificacao;
    codigoVendedor;
    porcentagemComissaoVenda;
    pedidosSalao;
    desconectarUsuarios;
    removerTagsComanda;
    acessaMenuVendas;
    aceitaTermoUso;
    dataAceitaTermoUso;
    percentualComissaoTaxaGarcom;
    podeGerarExcelFinanceiro;
    podeImprimirLancamentosFinanceiro;
    gestorDeImpressoes;
    lightTheme;
    idioma;
    twoAuthentication;
    codeVerificacao;
    usuarioLogado;
};
exports.Usuario = Usuario;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)({ name: 'ID' }),
    __metadata("design:type", Number)
], Usuario.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_HORA_USUARIO_ALT', type: 'datetime' }),
    __metadata("design:type", Date)
], Usuario.prototype, "dataHoraUsuarioAlt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_HORA_USUARIO_DEL', type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], Usuario.prototype, "dataHoraUsuarioDel", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_HORA_USUARIO_INC', type: 'datetime' }),
    __metadata("design:type", Date)
], Usuario.prototype, "dataHoraUsuarioInc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_SYNC', type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], Usuario.prototype, "dataSync", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IS_EXCLUIDO', length: 1 }),
    __metadata("design:type", String)
], Usuario.prototype, "isExcluido", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_ALT', length: 500 }),
    __metadata("design:type", String)
], Usuario.prototype, "usuarioAlt", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_DEL', length: 500, nullable: true }),
    __metadata("design:type", String)
], Usuario.prototype, "usuarioDel", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'USUARIO_INC', length: 500 }),
    __metadata("design:type", String)
], Usuario.prototype, "usuarioInc", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'UUID', length: 50, nullable: true }),
    __metadata("design:type", String)
], Usuario.prototype, "uuid", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'EMAIL', length: 100, nullable: true }),
    __metadata("design:type", String)
], Usuario.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'NOME_USUARIO', length: 100, nullable: true }),
    __metadata("design:type", String)
], Usuario.prototype, "nomeUsuario", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'SENHA', length: 100, nullable: true }),
    __metadata("design:type", String)
], Usuario.prototype, "senha", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'EMPRESA_ID', nullable: true }),
    __metadata("design:type", Number)
], Usuario.prototype, "empresaId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => empresa_entity_1.Empresa),
    (0, typeorm_1.JoinColumn)({ name: 'EMPRESA_ID' }),
    __metadata("design:type", empresa_entity_1.Empresa)
], Usuario.prototype, "empresa", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'IS_ADM_GERAL',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "admGeral", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'IS_ADM_LOCAL',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "admLocal", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'IS_OPERADOR',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "operador", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'IS_OPERADOR_ESTOQUE',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "operadorEstoque", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'TOKEN', type: 'text', nullable: true }),
    __metadata("design:type", String)
], Usuario.prototype, "token", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_EXPIRACAO_TOKEN', type: 'date', nullable: true }),
    __metadata("design:type", Date)
], Usuario.prototype, "dataExpiracaoToken", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'FL_TOUR_VISUALIZADO',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "flTourVisualizado", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'VISUALIZA_SALDOS_BANCARIOS',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "visualizaSaldosBancarios", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'GERENCIAR_LCTOS_FINANCEIROS',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "gerenciarLctosFinanceiros", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'RELATORIOS_FINANCEIROS',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "relatoriosFinanceiros", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'RELATORIOS_COMANDAS',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "relatoriosComandas", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'GERENCIAR_PRODUTOS',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "gerenciarProdutos", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'PODE_ACESSAR_FISCAL',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "podeAcessarFiscal", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'PODE_ACESSAR_CONFIGURACOES',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "podeAcessarConfiguracoes", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'PODE_VALIDAR_PDVS',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "podeValidarPdvs", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'ID_CLOUD', nullable: true }),
    __metadata("design:type", Number)
], Usuario.prototype, "idCloud", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'PODE_PAGAR_MENSALIDADE',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "podePagarMensalidade", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'PODE_DAR_CORTESIA',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "podeDarCortesia", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'PODE_LANCAR_INVENTARIO',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "podeLancarInventario", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'PODE_EDITAR_INVENTARIO',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "podeEditarInventario", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'DATA_ENVIO_ESQUECI_MINHA_SENHA',
        type: 'datetime',
        nullable: true,
    }),
    __metadata("design:type", Date)
], Usuario.prototype, "dataEnvioEsqueciMinhaSenha", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'SESSAO_USUARIO_LOGADO', nullable: true }),
    __metadata("design:type", Number)
], Usuario.prototype, "sessaoUsuarioLogado", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'SESSAO_USUARIO_LOGADO_MOBILE', nullable: true }),
    __metadata("design:type", Number)
], Usuario.prototype, "sessaoUsuarioLogadoMobile", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'PODE_VISUALIZAR_CAT_FIN_OCULTO',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "podeVisualizarCatFinOculto", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'PODE_VISUALIZAR_EMP_PROD_FINANCEIRO',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "podeVisualizarEmpProdFinanceiro", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'TELEFONE', length: 255, nullable: true }),
    __metadata("design:type", String)
], Usuario.prototype, "telefone", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'HASH_CONTATO_NOTIFICACAO', length: 255, nullable: true }),
    __metadata("design:type", String)
], Usuario.prototype, "hashContatoNotificacao", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CODIGO_VENDEDOR', nullable: true }),
    __metadata("design:type", Number)
], Usuario.prototype, "codigoVendedor", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'PORCENTAGEM_COMISSAO_VENDA',
        type: 'double',
        nullable: true,
    }),
    __metadata("design:type", Number)
], Usuario.prototype, "porcentagemComissaoVenda", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'PEDIDOS_SALAO',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "pedidosSalao", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'DESCONECTAR_USUARIOS',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "desconectarUsuarios", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'REMOVER_TAGS_COMANDA',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "removerTagsComanda", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'ACESSA_MENU_VENDAS',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "acessaMenuVendas", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'ACEITA_TERMO_USO',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "aceitaTermoUso", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'DATA_ACEITA_TERMO_USO', type: 'datetime', nullable: true }),
    __metadata("design:type", Date)
], Usuario.prototype, "dataAceitaTermoUso", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'PERCENTUAL_COMISSAO_TAXA_GARCOM',
        type: 'double',
        nullable: true,
    }),
    __metadata("design:type", Number)
], Usuario.prototype, "percentualComissaoTaxaGarcom", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'PODE_GERAR_EXCEL_FINANCEIRO',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "podeGerarExcelFinanceiro", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'PODE_IMPRIMIR_LANCAMENTOS_FINANCEIRO',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "podeImprimirLancamentosFinanceiro", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'GESTOR_DE_IMPRESSOES',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "gestorDeImpressoes", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'LIGHT_THEME',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "lightTheme", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'IDIOMA', length: 10, nullable: true }),
    __metadata("design:type", String)
], Usuario.prototype, "idioma", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'TWO_AUTHENTICATION',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "twoAuthentication", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'CODE_VERIFICACAO', length: 255, nullable: true }),
    __metadata("design:type", String)
], Usuario.prototype, "codeVerificacao", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'USUARIO_LOGADO',
        type: 'bit',
        nullable: true,
        transformer: bitTransformer,
    }),
    __metadata("design:type", Boolean)
], Usuario.prototype, "usuarioLogado", void 0);
exports.Usuario = Usuario = __decorate([
    (0, typeorm_1.Entity)({ name: 'usuario' })
], Usuario);
//# sourceMappingURL=usuario.entity.js.map