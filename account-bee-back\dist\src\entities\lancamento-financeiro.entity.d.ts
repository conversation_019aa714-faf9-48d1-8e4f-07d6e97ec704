import { Empresa } from './empresa.entity';
import { Pessoa } from './pessoa.entity';
import { Conta } from './conta.entity';
import { Local } from './local.entity';
import { Fornecedor } from './fornecedor.entity';
import { TipoLancamentoFinanceiro } from './tipo-lancamento-financeiro.entity';
import { CategoriaLancamento } from './categoria-lancamento.entity';
import { PlanoConta } from './plano-conta.entity';
import { FinanceiroCentroCusto } from './financeiro-centro-custo.entity';
export declare class LancamentoFinanceiro {
    id: number;
    descricao: string;
    dataLancamento?: Date;
    dataCompetencia?: Date;
    valorBruto?: string;
    valor?: string;
    observacao?: string;
    efetivado?: boolean;
    conciliado?: boolean;
    idTransacaoBancaria?: string;
    identificador?: string;
    empresaId: number;
    pessoaId: number;
    contaId?: number;
    localId?: number;
    fornecedorId?: number;
    tipoLancamentoFinanceiroId: number;
    categoriaLctoFinanceiroId?: number;
    planoContaCredito?: number;
    planoContaDebito?: number;
    empresa: Empresa;
    pessoa: Pessoa;
    conta?: Conta;
    local?: Local;
    fornecedor?: Fornecedor;
    tipoLancamentoFinanceiro: TipoLancamentoFinanceiro;
    categoriaLancamento?: CategoriaLancamento;
    planoContaCreditoEntity?: PlanoConta;
    planoContaDebitoEntity?: PlanoConta;
    financeiroCentroCustos: FinanceiroCentroCusto[];
    dataHoraUsuarioAlt: Date;
    dataHoraUsuarioDel?: Date;
    dataHoraUsuarioInc: Date;
    dataSync?: Date;
    isExcluido: string;
    usuarioAlt: string;
    usuarioDel?: string;
    usuarioInc: string;
    uuid?: string;
}
