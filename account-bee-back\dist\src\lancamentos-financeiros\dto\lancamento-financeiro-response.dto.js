"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LancamentoFinanceiroResponseDto = exports.CostCenterAllocationResponseDto = void 0;
class CostCenterAllocationResponseDto {
    id;
    centroCustoId;
    centroCustoNome;
    valor;
    porcentagem;
}
exports.CostCenterAllocationResponseDto = CostCenterAllocationResponseDto;
class LancamentoFinanceiroResponseDto {
    id;
    descricao;
    dataLancamento;
    dataCompetencia;
    valorBruto;
    valor;
    observacao;
    efetivado;
    conciliado;
    empresaId;
    pessoaId;
    pessoaNome;
    contaId;
    contaNome;
    localId;
    localNome;
    fornecedorId;
    fornecedorNome;
    tipoLancamentoFinanceiroId;
    categoriaLctoFinanceiroId;
    categoriaNome;
    planoContaCredito;
    planoContaCreditoNome;
    alocacoesCentroCusto;
    dataHoraUsuarioInc;
    dataHoraUsuarioAlt;
    usuarioInc;
    usuarioAlt;
}
exports.LancamentoFinanceiroResponseDto = LancamentoFinanceiroResponseDto;
//# sourceMappingURL=lancamento-financeiro-response.dto.js.map