"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
let UserResponseDto = class UserResponseDto {
    id;
    nomeUsuario;
    email;
    telefone;
    empresaId;
    admGeral;
    admLocal;
    operador;
    operadorEstoque;
    visualizaSaldosBancarios;
    gerenciarLctosFinanceiros;
    relatoriosFinanceiros;
    relatoriosComandas;
    gerenciarProdutos;
    podeAcessarFiscal;
    podeAcessarConfiguracoes;
    isExcluido;
    dataHoraUsuarioInc;
    dataHoraUsuarioAlt;
    usuarioAlt;
    twoAuthentication;
    podeVisualizarCatFinOculto;
    desconectarUsuarios;
    podeGerarExcelFinanceiro;
    podeImprimirLancamentosFinanceiro;
    usuarioLogado;
};
exports.UserResponseDto = UserResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID do usuário',
        example: 1,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], UserResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome completo do usuário',
        example: 'João Silva',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], UserResponseDto.prototype, "nomeUsuario", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Email do usuário',
        example: '<EMAIL>',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], UserResponseDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Telefone do usuário',
        example: '(11) 98765-4321',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], UserResponseDto.prototype, "telefone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da empresa',
        example: 1,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], UserResponseDto.prototype, "empresaId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Define se o usuário é administrador geral',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ value }) => !!value),
    __metadata("design:type", Boolean)
], UserResponseDto.prototype, "admGeral", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Define se o usuário é administrador local',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ value }) => !!value),
    __metadata("design:type", Boolean)
], UserResponseDto.prototype, "admLocal", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Define se o usuário é operador',
        example: true,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ value }) => !!value),
    __metadata("design:type", Boolean)
], UserResponseDto.prototype, "operador", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Define se o usuário é operador de estoque',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ value }) => !!value),
    __metadata("design:type", Boolean)
], UserResponseDto.prototype, "operadorEstoque", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permite visualizar saldos bancários',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ value }) => !!value),
    __metadata("design:type", Boolean)
], UserResponseDto.prototype, "visualizaSaldosBancarios", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permite gerenciar lançamentos financeiros',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ value }) => !!value),
    __metadata("design:type", Boolean)
], UserResponseDto.prototype, "gerenciarLctosFinanceiros", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permite acessar relatórios financeiros',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ value }) => !!value),
    __metadata("design:type", Boolean)
], UserResponseDto.prototype, "relatoriosFinanceiros", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permite acessar relatórios de comandas',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ value }) => !!value),
    __metadata("design:type", Boolean)
], UserResponseDto.prototype, "relatoriosComandas", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permite gerenciar produtos',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ value }) => !!value),
    __metadata("design:type", Boolean)
], UserResponseDto.prototype, "gerenciarProdutos", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permite acessar módulo fiscal',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ value }) => !!value),
    __metadata("design:type", Boolean)
], UserResponseDto.prototype, "podeAcessarFiscal", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permite acessar configurações',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ value }) => !!value),
    __metadata("design:type", Boolean)
], UserResponseDto.prototype, "podeAcessarConfiguracoes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status de exclusão do usuário',
        example: 'N',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], UserResponseDto.prototype, "isExcluido", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de criação do usuário',
        example: '2024-01-01T00:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], UserResponseDto.prototype, "dataHoraUsuarioInc", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data da última alteração',
        example: '2024-01-01T00:00:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], UserResponseDto.prototype, "dataHoraUsuarioAlt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Usuário que fez a última alteração',
        example: '<EMAIL>',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], UserResponseDto.prototype, "usuarioAlt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Define se usa autenticação em dois fatores',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ value }) => !!value),
    __metadata("design:type", Boolean)
], UserResponseDto.prototype, "twoAuthentication", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permite visualizar categorias financeiras ocultas',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ value }) => !!value),
    __metadata("design:type", Boolean)
], UserResponseDto.prototype, "podeVisualizarCatFinOculto", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permite desconectar outros usuários',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ value }) => !!value),
    __metadata("design:type", Boolean)
], UserResponseDto.prototype, "desconectarUsuarios", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permite gerar arquivos Excel de relatórios financeiros',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ value }) => !!value),
    __metadata("design:type", Boolean)
], UserResponseDto.prototype, "podeGerarExcelFinanceiro", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Permite imprimir lançamentos financeiros',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ value }) => !!value),
    __metadata("design:type", Boolean)
], UserResponseDto.prototype, "podeImprimirLancamentosFinanceiro", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Define se o usuário está logado',
        example: false,
    }),
    (0, class_transformer_1.Expose)(),
    (0, class_transformer_1.Transform)(({ value }) => !!value),
    __metadata("design:type", Boolean)
], UserResponseDto.prototype, "usuarioLogado", void 0);
exports.UserResponseDto = UserResponseDto = __decorate([
    (0, class_transformer_1.Exclude)()
], UserResponseDto);
//# sourceMappingURL=user-response.dto.js.map