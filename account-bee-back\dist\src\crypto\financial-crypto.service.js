"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FinancialCryptoService = void 0;
const common_1 = require("@nestjs/common");
const legacy_crypto_service_1 = require("./legacy-crypto.service");
let FinancialCryptoService = class FinancialCryptoService {
    legacyCryptoService;
    constructor(legacyCryptoService) {
        this.legacyCryptoService = legacyCryptoService;
    }
    encryptValue(value, empresaId) {
        if (value === null || value === undefined) {
            return this.legacyCryptoService.encrypt('0.0', empresaId) || '0.0';
        }
        const stringValue = typeof value === 'number' ? value.toString() : value;
        if (stringValue.length > 20) {
            return stringValue;
        }
        const encrypted = this.legacyCryptoService.encrypt(stringValue, empresaId);
        return encrypted || stringValue;
    }
    decryptValueAsNumber(encryptedValue, empresaId) {
        if (!encryptedValue) {
            return 0.0;
        }
        if (encryptedValue.length <= 20) {
            return parseFloat(encryptedValue) || 0.0;
        }
        const decrypted = this.legacyCryptoService.decrypt(encryptedValue, empresaId);
        if (decrypted) {
            return parseFloat(decrypted) || 0.0;
        }
        return 0.0;
    }
    decryptValueAsString(encryptedValue, empresaId) {
        if (!encryptedValue) {
            return '0.0';
        }
        if (encryptedValue.length <= 20) {
            return encryptedValue;
        }
        const decrypted = this.legacyCryptoService.decrypt(encryptedValue, empresaId);
        return decrypted || '0.0';
    }
    isEncrypted(value) {
        return Boolean(value && value.length > 20);
    }
    encryptMultipleValues(values, empresaId) {
        const result = {};
        for (const [key, value] of Object.entries(values)) {
            result[key] = this.encryptValue(value, empresaId);
        }
        return result;
    }
    decryptMultipleValues(encryptedValues, empresaId) {
        const result = {};
        for (const [key, encryptedValue] of Object.entries(encryptedValues)) {
            result[key] = this.decryptValueAsNumber(encryptedValue, empresaId);
        }
        return result;
    }
    validateEncryption(value, empresaId) {
        try {
            const encrypted = this.encryptValue(value, empresaId);
            const decrypted = this.decryptValueAsNumber(encrypted, empresaId);
            const originalValue = typeof value === 'number' ? value : parseFloat(value);
            return Math.abs(decrypted - originalValue) < 0.01;
        }
        catch (error) {
            return false;
        }
    }
};
exports.FinancialCryptoService = FinancialCryptoService;
exports.FinancialCryptoService = FinancialCryptoService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [legacy_crypto_service_1.LegacyCryptoService])
], FinancialCryptoService);
//# sourceMappingURL=financial-crypto.service.js.map