"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PessoaResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
class PessoaResponseDto {
    id;
    nome;
    dataNascimento;
    email;
    cpf;
    cnpj;
    cep;
    endereco;
    telefone;
    observacoes;
    empresaId;
    uuid;
    dataHoraUsuarioInc;
    dataHoraUsuarioAlt;
    usuarioInc;
    usuarioAlt;
    isExcluido;
}
exports.PessoaResponseDto = PessoaResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID único da pessoa',
        example: 1,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], PessoaResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nome completo da pessoa',
        example: 'João Silva Santos',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], PessoaResponseDto.prototype, "nome", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de nascimento da pessoa',
        example: '1990-05-15',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], PessoaResponseDto.prototype, "dataNascimento", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Email da pessoa',
        example: '<EMAIL>',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], PessoaResponseDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'CPF da pessoa (descriptografado)',
        example: '123.456.789-01',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], PessoaResponseDto.prototype, "cpf", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'CNPJ da pessoa (descriptografado)',
        example: '12.345.678/0001-90',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], PessoaResponseDto.prototype, "cnpj", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'CEP da pessoa',
        example: '12345-678',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], PessoaResponseDto.prototype, "cep", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Endereço completo da pessoa',
        example: 'Rua das Flores, 123, Bairro Centro, Cidade - Estado',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], PessoaResponseDto.prototype, "endereco", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Telefone da pessoa',
        example: '(11) 98765-4321',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], PessoaResponseDto.prototype, "telefone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Observações sobre a pessoa',
        example: 'Cliente preferencial, sempre pontual nos pagamentos',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], PessoaResponseDto.prototype, "observacoes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da empresa à qual a pessoa pertence',
        example: 1,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Number)
], PessoaResponseDto.prototype, "empresaId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'UUID único da pessoa',
        example: 'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        required: false,
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], PessoaResponseDto.prototype, "uuid", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data de criação do registro',
        example: '2024-07-14T10:30:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], PessoaResponseDto.prototype, "dataHoraUsuarioInc", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Data da última alteração',
        example: '2024-07-14T15:45:00.000Z',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", Date)
], PessoaResponseDto.prototype, "dataHoraUsuarioAlt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Usuário que criou o registro',
        example: '<EMAIL>',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], PessoaResponseDto.prototype, "usuarioInc", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Usuário que fez a última alteração',
        example: '<EMAIL>',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], PessoaResponseDto.prototype, "usuarioAlt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status do registro (N = Ativo, S = Excluído)',
        example: 'N',
    }),
    (0, class_transformer_1.Expose)(),
    __metadata("design:type", String)
], PessoaResponseDto.prototype, "isExcluido", void 0);
//# sourceMappingURL=pessoa-response.dto.js.map