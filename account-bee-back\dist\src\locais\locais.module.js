"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocaisModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const locais_service_1 = require("./locais.service");
const locais_controller_1 = require("./locais.controller");
const local_entity_1 = require("../entities/local.entity");
const empresa_entity_1 = require("../entities/empresa.entity");
const crypto_module_1 = require("../crypto/crypto.module");
let LocaisModule = class LocaisModule {
};
exports.LocaisModule = LocaisModule;
exports.LocaisModule = LocaisModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([local_entity_1.Local, empresa_entity_1.Empresa]), crypto_module_1.CryptoModule],
        controllers: [locais_controller_1.LocaisController],
        providers: [locais_service_1.LocaisService],
        exports: [locais_service_1.LocaisService],
    })
], LocaisModule);
//# sourceMappingURL=locais.module.js.map