import { PessoasService } from './pessoas.service';
import { CreatePessoaDto } from './dto/create-pessoa.dto';
import { UpdatePessoaDto } from './dto/update-pessoa.dto';
import { PessoaResponseDto } from './dto/pessoa-response.dto';
import { FilterPessoasDto } from './dto/filter-pessoas.dto';
export declare class PessoasController {
    private readonly pessoasService;
    constructor(pessoasService: PessoasService);
    create(createPessoaDto: CreatePessoaDto, req: any): Promise<PessoaResponseDto>;
    findAll(filterDto: FilterPessoasDto, req: any): Promise<{
        data: PessoaResponseDto[];
        total: number;
        page: number;
        limit: number;
    }>;
    findByEmpresa(empresaId: string, req: any): Promise<PessoaResponseDto[]>;
    findOne(id: string, req: any): Promise<PessoaResponseDto>;
    update(id: string, updatePessoaDto: UpdatePessoaDto, req: any): Promise<PessoaResponseDto>;
    toggleStatus(id: string, req: any): Promise<PessoaResponseDto>;
    remove(id: string, req: any): Promise<void>;
}
