import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { CryptoModule } from './crypto/crypto.module';
import { UsersModule } from './users/users.module';
import { EmpresaModule } from './empresa/empresa.module';
import { PessoasModule } from './pessoas/pessoas.module';
import { LocaisModule } from './locais/locais.module';
import { ContasModule } from './contas/contas.module';
import { CentroCustoModule } from './centro-custo/centro-custo.module';
import { FornecedorModule } from './fornecedores/fornecedor.routes';
import { LancamentosFinanceirosModule } from './lancamentos-financeiros/lancamentos-financeiros.module';
import { CategoriasLancamentoModule } from './categorias-lancamento/categorias-lancamento.module';
import { PlanoContasModule } from './plano-contas/plano-contas.module';
import { Usuario } from './entities/usuario.entity';
import { Empresa } from './entities/empresa.entity';
import { Local } from './entities/local.entity';
import { Pessoa } from './entities/pessoa.entity';
import { Conta } from './entities/conta.entity';
import { CentroCusto } from './entities/centro-custo.entity';
import { Fornecedor } from './entities/fornecedor.entity';
import { LancamentoFinanceiro } from './entities/lancamento-financeiro.entity';
import { FinanceiroCentroCusto } from './entities/financeiro-centro-custo.entity';
import { TipoLancamentoFinanceiro } from './entities/tipo-lancamento-financeiro.entity';
import { CategoriaLancamento } from './entities/categoria-lancamento.entity';
import { PlanoConta } from './entities/plano-conta.entity';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get('DB_HOST'),
        port: configService.get('DB_PORT'),
        username: configService.get('DB_USERNAME'),
        password: configService.get('DB_PASSWORD'),
        database: configService.get('DB_DATABASE'),
        entities: [
          Usuario,
          Empresa,
          Local,
          Pessoa,
          Conta,
          CentroCusto,
          Fornecedor,
          LancamentoFinanceiro,
          FinanceiroCentroCusto,
          TipoLancamentoFinanceiro,
          CategoriaLancamento,
          PlanoConta,
        ],
        synchronize: false, // Nunca use true em produção!
        logging: configService.get('NODE_ENV') === 'development',

        // Configurações para conexões mais robustas
        extra: {
          connectionLimit: 10,
          acquireTimeout: 60000,
          timeout: 60000,
          reconnect: true,
          idleTimeout: 300000,
        },

        // Pool de conexões
        poolSize: 10,

        // Retry de conexão
        retryAttempts: 5,
        retryDelay: 3000,

        // Configurações de timeout
        connectTimeout: 60000,
        acquireTimeout: 60000,
      }),
      inject: [ConfigService],
    }),
    AuthModule,
    CryptoModule,
    UsersModule,
    EmpresaModule,
    PessoasModule,
    LocaisModule,
    ContasModule,
    CentroCustoModule,
    FornecedorModule,
    LancamentosFinanceirosModule,
    CategoriasLancamentoModule,
    PlanoContasModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
