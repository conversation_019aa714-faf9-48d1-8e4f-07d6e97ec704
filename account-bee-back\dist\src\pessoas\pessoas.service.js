"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PessoasService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const pessoa_entity_1 = require("../entities/pessoa.entity");
const empresa_entity_1 = require("../entities/empresa.entity");
const pessoa_response_dto_1 = require("./dto/pessoa-response.dto");
const pessoa_crypto_service_1 = require("../crypto/pessoa-crypto.service");
const class_transformer_1 = require("class-transformer");
const base_service_1 = require("../common/services/base.service");
let PessoasService = class PessoasService extends base_service_1.BaseService {
    pessoaRepository;
    empresaRepository;
    cryptoService;
    constructor(pessoaRepository, empresaRepository, cryptoService) {
        super();
        this.pessoaRepository = pessoaRepository;
        this.empresaRepository = empresaRepository;
        this.cryptoService = cryptoService;
    }
    async create(createPessoaDto, currentUser) {
        let empresaId = createPessoaDto.empresaId;
        if (!currentUser.admGeral || !empresaId) {
            empresaId = currentUser.empresaId;
        }
        const empresa = await this.empresaRepository.findOne({
            where: { id: empresaId },
            select: [
                'id',
                'nomeEmpresa',
                'razaoSocial',
                'telefone',
                'ddd',
                'horaFimExpediente',
                'statusEmpresa',
                'isExcluido',
            ],
        });
        if (!empresa) {
            throw new common_1.BadRequestException('Empresa não encontrada');
        }
        if (!currentUser.admGeral &&
            createPessoaDto.empresaId &&
            createPessoaDto.empresaId !== currentUser.empresaId) {
            throw new common_1.BadRequestException('Você só pode criar pessoas para sua própria empresa');
        }
        await this.checkDuplicates(createPessoaDto, empresaId);
        const normalizedDto = this.normalizeSensitiveData(createPessoaDto);
        const encryptedData = await this.encryptSensitiveData(normalizedDto, empresaId);
        const now = new Date();
        const pessoa = this.pessoaRepository.create({
            ...createPessoaDto,
            ...encryptedData,
            empresaId: empresaId,
            isExcluido: 'N',
            dataHoraUsuarioInc: now,
            dataHoraUsuarioAlt: now,
            usuarioInc: currentUser.email || 'sistema',
            usuarioAlt: currentUser.email || 'sistema',
            dataNascimento: createPessoaDto.dataNascimento
                ? new Date(createPessoaDto.dataNascimento)
                : undefined,
        });
        const savedPessoa = await this.pessoaRepository.save(pessoa);
        return this.preparePessoaResponse(savedPessoa);
    }
    async findAll(filterDto, currentUser) {
        const { page = 1, limit = 10, search, empresaId, status = 'N', tipo, } = filterDto;
        const where = {};
        if (status !== 'all') {
            where.isExcluido = status;
        }
        if (!currentUser.admGeral) {
            where.empresaId = currentUser.empresaId;
        }
        else {
            if (!empresaId) {
                throw new common_1.BadRequestException('empresaId must be a number conforming to the specified constraints');
            }
            where.empresaId = empresaId;
        }
        const queryBuilder = this.pessoaRepository.createQueryBuilder('pessoa');
        queryBuilder.where(where);
        if (search) {
            queryBuilder.andWhere('(pessoa.nome LIKE :search OR pessoa.email LIKE :search)', { search: `%${search}%` });
        }
        if (tipo && tipo !== 'all') {
            if (tipo === 'cpf') {
                queryBuilder.andWhere('pessoa.cpf IS NOT NULL');
            }
            else if (tipo === 'cnpj') {
                queryBuilder.andWhere('pessoa.cnpj IS NOT NULL');
            }
        }
        const { page: validatedPage, limit: validatedLimit } = this.validatePaginationParams(page, limit);
        this.applyDefaultSorting(queryBuilder, 'pessoa');
        this.applyPagination(queryBuilder, validatedPage, validatedLimit);
        const [pessoas, total] = await queryBuilder.getManyAndCount();
        return this.createPaginatedResponse(pessoas.map((pessoa) => this.preparePessoaResponse(pessoa)), total, validatedPage, validatedLimit);
    }
    async findOne(id, currentUser) {
        const pessoa = await this.pessoaRepository.findOne({
            where: { id, isExcluido: 'N' },
        });
        if (!pessoa) {
            throw new common_1.NotFoundException('Pessoa não encontrada');
        }
        if (!currentUser.admGeral && pessoa.empresaId !== currentUser.empresaId) {
            throw new common_1.ForbiddenException('Sem permissão para visualizar esta pessoa');
        }
        return this.preparePessoaResponse(pessoa);
    }
    async update(id, updatePessoaDto, currentUser) {
        const pessoa = await this.pessoaRepository.findOne({
            where: { id, isExcluido: 'N' },
        });
        if (!pessoa) {
            throw new common_1.NotFoundException('Pessoa não encontrada');
        }
        if (!currentUser.admGeral && pessoa.empresaId !== currentUser.empresaId) {
            throw new common_1.ForbiddenException('Sem permissão para editar esta pessoa');
        }
        await this.checkDuplicates(updatePessoaDto, pessoa.empresaId, id);
        const normalizedDto = this.normalizeSensitiveData(updatePessoaDto);
        const encryptedData = await this.encryptSensitiveData(normalizedDto, pessoa.empresaId);
        Object.assign(pessoa, updatePessoaDto, encryptedData);
        pessoa.dataHoraUsuarioAlt = new Date();
        pessoa.usuarioAlt = currentUser.email || 'sistema';
        if (updatePessoaDto.dataNascimento) {
            pessoa.dataNascimento = new Date(updatePessoaDto.dataNascimento);
        }
        const updatedPessoa = await this.pessoaRepository.save(pessoa);
        return this.preparePessoaResponse(updatedPessoa);
    }
    async remove(id, currentUser) {
        const pessoa = await this.pessoaRepository.findOne({
            where: { id, isExcluido: 'N' },
        });
        if (!pessoa) {
            throw new common_1.NotFoundException('Pessoa não encontrada');
        }
        if (!currentUser.admGeral && pessoa.empresaId !== currentUser.empresaId) {
            throw new common_1.ForbiddenException('Sem permissão para excluir esta pessoa');
        }
        pessoa.isExcluido = 'S';
        pessoa.dataHoraUsuarioDel = new Date();
        pessoa.usuarioDel = currentUser.email || 'sistema';
        await this.pessoaRepository.save(pessoa);
    }
    async toggleStatus(id, currentUser) {
        const pessoa = await this.pessoaRepository.findOne({
            where: { id },
        });
        if (!pessoa) {
            throw new common_1.NotFoundException('Pessoa não encontrada');
        }
        if (!currentUser.admGeral && pessoa.empresaId !== currentUser.empresaId) {
            throw new common_1.ForbiddenException('Sem permissão para alterar status desta pessoa');
        }
        pessoa.isExcluido = pessoa.isExcluido === 'N' ? 'S' : 'N';
        pessoa.dataHoraUsuarioAlt = new Date();
        pessoa.usuarioAlt = currentUser.email || 'sistema';
        const updatedPessoa = await this.pessoaRepository.save(pessoa);
        return this.preparePessoaResponse(updatedPessoa);
    }
    async findByEmpresa(empresaId, currentUser) {
        if (!currentUser.admGeral && empresaId !== currentUser.empresaId) {
            throw new common_1.ForbiddenException('Sem permissão para visualizar pessoas desta empresa');
        }
        const queryBuilder = this.pessoaRepository.createQueryBuilder('pessoa');
        queryBuilder.where({ empresaId, isExcluido: 'N' });
        this.applyDefaultSorting(queryBuilder, 'pessoa');
        const pessoas = await queryBuilder.getMany();
        return pessoas.map((pessoa) => this.preparePessoaResponse(pessoa));
    }
    async checkDuplicates(pessoaDto, empresaId, excludeId) {
        const where = {
            empresaId,
            isExcluido: 'N',
        };
        if (excludeId) {
            where.id = (0, typeorm_2.Not)(excludeId);
        }
        if (pessoaDto.email) {
            const encryptedEmail = this.cryptoService.encryptEmail(pessoaDto.email, empresaId);
            if (encryptedEmail) {
                const existingByEmail = await this.pessoaRepository.findOne({
                    where: { ...where, email: encryptedEmail },
                });
                if (existingByEmail) {
                    throw new common_1.ConflictException('Email já cadastrado para esta empresa');
                }
            }
        }
        if (pessoaDto.cpf) {
            const encryptedCpf = this.cryptoService.encryptCpf(pessoaDto.cpf, empresaId);
            if (encryptedCpf) {
                const existingByCpf = await this.pessoaRepository.findOne({
                    where: { ...where, cpf: encryptedCpf },
                });
                if (existingByCpf) {
                    throw new common_1.ConflictException('CPF já cadastrado para esta empresa');
                }
            }
        }
        if (pessoaDto.cnpj) {
            const encryptedCnpj = this.cryptoService.encryptCnpj(pessoaDto.cnpj, empresaId);
            if (encryptedCnpj) {
                const existingByCnpj = await this.pessoaRepository.findOne({
                    where: { ...where, cnpj: encryptedCnpj },
                });
                if (existingByCnpj) {
                    throw new common_1.ConflictException('CNPJ já cadastrado para esta empresa');
                }
            }
        }
    }
    async encryptSensitiveData(pessoaDto, empresaId) {
        const encryptedData = {};
        if (pessoaDto.email) {
            const encryptedEmail = this.cryptoService.encryptEmail(pessoaDto.email, empresaId);
            if (!encryptedEmail) {
                throw new common_1.BadRequestException('Erro ao criptografar email');
            }
            encryptedData.email = encryptedEmail;
        }
        if (pessoaDto.cpf) {
            const encryptedCpf = this.cryptoService.encryptCpf(pessoaDto.cpf, empresaId);
            if (!encryptedCpf) {
                throw new common_1.BadRequestException('Erro ao criptografar CPF');
            }
            encryptedData.cpf = encryptedCpf;
        }
        if (pessoaDto.cnpj) {
            const encryptedCnpj = this.cryptoService.encryptCnpj(pessoaDto.cnpj, empresaId);
            if (!encryptedCnpj) {
                throw new common_1.BadRequestException('Erro ao criptografar CNPJ');
            }
            encryptedData.cnpj = encryptedCnpj;
        }
        return encryptedData;
    }
    preparePessoaResponse(pessoa) {
        const decryptedPessoa = { ...pessoa };
        if (pessoa.email) {
            const decryptedEmail = this.decryptField(pessoa.email, pessoa.empresaId);
            decryptedPessoa.email = decryptedEmail || pessoa.email;
        }
        if (pessoa.cpf) {
            const decryptedCpf = this.decryptField(pessoa.cpf, pessoa.empresaId);
            decryptedPessoa.cpf = decryptedCpf || pessoa.cpf;
        }
        if (pessoa.cnpj) {
            const decryptedCnpj = this.decryptField(pessoa.cnpj, pessoa.empresaId);
            decryptedPessoa.cnpj = decryptedCnpj || pessoa.cnpj;
        }
        return (0, class_transformer_1.plainToClass)(pessoa_response_dto_1.PessoaResponseDto, decryptedPessoa, {
            excludeExtraneousValues: true,
        });
    }
    decryptField(encryptedValue, empresaId = 0) {
        if (encryptedValue &&
            !encryptedValue.includes('=') &&
            !encryptedValue.includes('+')) {
            return encryptedValue;
        }
        const decrypted = this.cryptoService.decrypt(encryptedValue, empresaId);
        if (decrypted && decrypted.length > 0) {
            return decrypted;
        }
        return null;
    }
    normalizeSensitiveData(pessoaDto) {
        const normalized = { ...pessoaDto };
        if (normalized.cpf) {
            normalized.cpf = normalized.cpf.replace(/\D/g, '');
        }
        if (normalized.cnpj) {
            normalized.cnpj = normalized.cnpj.replace(/\D/g, '');
        }
        return normalized;
    }
};
exports.PessoasService = PessoasService;
exports.PessoasService = PessoasService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(pessoa_entity_1.Pessoa)),
    __param(1, (0, typeorm_1.InjectRepository)(empresa_entity_1.Empresa)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        pessoa_crypto_service_1.PessoaCryptoService])
], PessoasService);
//# sourceMappingURL=pessoas.service.js.map