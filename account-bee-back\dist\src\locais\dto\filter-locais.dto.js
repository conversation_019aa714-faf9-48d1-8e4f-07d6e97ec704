"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FilterLocaisDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class FilterLocaisDto {
    page = 1;
    limit = 10;
    search;
    idEmpresa;
    status = 'N';
    idCidade;
    cep;
    bairro;
}
exports.FilterLocaisDto = FilterLocaisDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Número da página',
        example: 1,
        minimum: 1,
        default: 1,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: 'Página deve ser um número' }),
    (0, class_validator_1.Min)(1, { message: 'Página deve ser maior que 0' }),
    __metadata("design:type", Number)
], FilterLocaisDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Quantidade de itens por página',
        example: 10,
        minimum: 1,
        maximum: 100,
        default: 10,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: 'Limite deve ser um número' }),
    (0, class_validator_1.Min)(1, { message: 'Limite deve ser maior que 0' }),
    (0, class_validator_1.Max)(100, { message: 'Limite deve ser menor ou igual a 100' }),
    __metadata("design:type", Number)
], FilterLocaisDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Termo de busca (nome, endereço, bairro)',
        example: 'Centro',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Termo de busca deve ser uma string' }),
    __metadata("design:type", String)
], FilterLocaisDto.prototype, "search", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID da empresa para filtrar locais',
        example: 1,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: 'ID da empresa deve ser um número' }),
    __metadata("design:type", Number)
], FilterLocaisDto.prototype, "idEmpresa", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status do local',
        example: 'N',
        enum: ['N', 'S', 'all'],
        default: 'N',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsIn)(['N', 'S', 'all'], {
        message: 'Status deve ser N (ativo), S (inativo) ou all (todos)',
    }),
    __metadata("design:type", String)
], FilterLocaisDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Filtrar por cidade',
        example: 1,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)({}, { message: 'ID da cidade deve ser um número' }),
    __metadata("design:type", Number)
], FilterLocaisDto.prototype, "idCidade", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Filtrar por CEP',
        example: '12345-678',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'CEP deve ser uma string' }),
    __metadata("design:type", String)
], FilterLocaisDto.prototype, "cep", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Filtrar por bairro',
        example: 'Centro',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Bairro deve ser uma string' }),
    __metadata("design:type", String)
], FilterLocaisDto.prototype, "bairro", void 0);
//# sourceMappingURL=filter-locais.dto.js.map