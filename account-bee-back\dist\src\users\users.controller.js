"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const users_service_1 = require("./users.service");
const create_user_dto_1 = require("./dto/create-user.dto");
const update_user_dto_1 = require("./dto/update-user.dto");
const update_password_dto_1 = require("./dto/update-password.dto");
const user_response_dto_1 = require("./dto/user-response.dto");
const filter_users_dto_1 = require("./dto/filter-users.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../common/guards/roles.guard");
const permissions_guard_1 = require("../common/guards/permissions.guard");
const roles_decorator_1 = require("../common/decorators/roles.decorator");
const permissions_decorator_1 = require("../common/decorators/permissions.decorator");
const user_roles_enum_1 = require("../common/enums/user-roles.enum");
let UsersController = class UsersController {
    usersService;
    constructor(usersService) {
        this.usersService = usersService;
    }
    create(createUserDto, req) {
        return this.usersService.create(createUserDto, req.user);
    }
    findAll(filterDto, req) {
        return this.usersService.findAll(filterDto, req.user);
    }
    findByEmpresa(empresaId, req) {
        return this.usersService.findByEmpresa(+empresaId, req.user);
    }
    findOne(id, req) {
        return this.usersService.findOne(+id, req.user);
    }
    update(id, updateUserDto, req) {
        return this.usersService.update(+id, updateUserDto, req.user);
    }
    updatePassword(id, updatePasswordDto, req) {
        return this.usersService.updatePassword(+id, updatePasswordDto, req.user);
    }
    toggleStatus(id, req) {
        return this.usersService.toggleStatus(+id, req.user);
    }
    async remove(id, req) {
        await this.usersService.remove(+id, req.user);
    }
};
exports.UsersController = UsersController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(user_roles_enum_1.UserRole.ADMIN_GERAL, user_roles_enum_1.UserRole.ADMIN_LOCAL),
    (0, permissions_decorator_1.RequirePermissions)(user_roles_enum_1.UserPermission.USERS_CREATE),
    (0, swagger_1.ApiOperation)({ summary: 'Criar novo usuário' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Usuário criado com sucesso',
        type: user_response_dto_1.UserResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Email já cadastrado' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_user_dto_1.CreateUserDto, Object]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, permissions_decorator_1.RequirePermissions)(user_roles_enum_1.UserPermission.USERS_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Listar usuários com filtros e paginação' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de usuários',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: { $ref: '#/components/schemas/UserResponseDto' },
                },
                total: { type: 'number' },
                page: { type: 'number' },
                limit: { type: 'number' },
            },
        },
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [filter_users_dto_1.FilterUsersDto, Object]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('empresa/:empresaId'),
    (0, permissions_decorator_1.RequirePermissions)(user_roles_enum_1.UserPermission.USERS_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Listar usuários por empresa' }),
    (0, swagger_1.ApiParam)({ name: 'empresaId', description: 'ID da empresa' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de usuários da empresa',
        type: [user_response_dto_1.UserResponseDto],
    }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Sem permissão' }),
    __param(0, (0, common_1.Param)('empresaId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "findByEmpresa", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, permissions_decorator_1.RequirePermissions)(user_roles_enum_1.UserPermission.USERS_READ),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar usuário por ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID do usuário' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dados do usuário',
        type: user_response_dto_1.UserResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Usuário não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Sem permissão' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)(user_roles_enum_1.UserRole.ADMIN_GERAL, user_roles_enum_1.UserRole.ADMIN_LOCAL),
    (0, permissions_decorator_1.RequirePermissions)(user_roles_enum_1.UserPermission.USERS_UPDATE),
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar usuário' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID do usuário' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Usuário atualizado com sucesso',
        type: user_response_dto_1.UserResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Usuário não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Sem permissão' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Email já cadastrado' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_user_dto_1.UpdateUserDto, Object]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/password'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, swagger_1.ApiOperation)({ summary: 'Alterar senha do usuário' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID do usuário' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Senha alterada com sucesso' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Senha atual incorreta' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Usuário não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Sem permissão' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_password_dto_1.UpdatePasswordDto, Object]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "updatePassword", null);
__decorate([
    (0, common_1.Patch)(':id/status'),
    (0, roles_decorator_1.Roles)(user_roles_enum_1.UserRole.ADMIN_GERAL, user_roles_enum_1.UserRole.ADMIN_LOCAL),
    (0, permissions_decorator_1.RequirePermissions)(user_roles_enum_1.UserPermission.USERS_UPDATE),
    (0, swagger_1.ApiOperation)({ summary: 'Ativar/desativar usuário' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID do usuário' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Status alterado com sucesso',
        type: user_response_dto_1.UserResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Usuário não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Sem permissão' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], UsersController.prototype, "toggleStatus", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, roles_decorator_1.Roles)(user_roles_enum_1.UserRole.ADMIN_GERAL),
    (0, permissions_decorator_1.RequirePermissions)(user_roles_enum_1.UserPermission.USERS_DELETE),
    (0, swagger_1.ApiOperation)({ summary: 'Excluir usuário (soft delete)' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID do usuário' }),
    (0, swagger_1.ApiResponse)({ status: 204, description: 'Usuário excluído com sucesso' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Usuário não encontrado' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Sem permissão' }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Não é possível excluir o próprio usuário',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "remove", null);
exports.UsersController = UsersController = __decorate([
    (0, swagger_1.ApiTags)('users'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard, permissions_guard_1.PermissionsGuard),
    (0, common_1.Controller)('users'),
    __metadata("design:paramtypes", [users_service_1.UsersService])
], UsersController);
//# sourceMappingURL=users.controller.js.map