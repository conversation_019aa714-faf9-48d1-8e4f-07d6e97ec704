import { Empresa } from './empresa.entity';
export declare class Usuario {
    id: number;
    dataHoraUsuarioAlt: Date;
    dataHoraUsuarioDel?: Date;
    dataHoraUsuarioInc: Date;
    dataSync?: Date;
    isExcluido: string;
    usuarioAlt: string;
    usuarioDel?: string;
    usuarioInc: string;
    uuid?: string;
    email?: string;
    nomeUsuario?: string;
    senha?: string;
    empresaId?: number;
    empresa?: Empresa;
    admGeral?: boolean;
    admLocal?: boolean;
    operador?: boolean;
    operadorEstoque?: boolean;
    token?: string;
    dataExpiracaoToken?: Date;
    flTourVisualizado?: boolean;
    visualizaSaldosBancarios?: boolean;
    gerenciarLctosFinanceiros?: boolean;
    relatoriosFinanceiros?: boolean;
    relatoriosComandas?: boolean;
    gerenciarProdutos?: boolean;
    podeAcessarFiscal?: boolean;
    podeAcessarConfiguracoes?: boolean;
    podeValidarPdvs?: boolean;
    idCloud?: number;
    podePagarMensalidade?: boolean;
    podeDarCortesia?: boolean;
    podeLancarInventario?: boolean;
    podeEditarInventario?: boolean;
    dataEnvioEsqueciMinhaSenha?: Date;
    sessaoUsuarioLogado?: number;
    sessaoUsuarioLogadoMobile?: number;
    podeVisualizarCatFinOculto?: boolean;
    podeVisualizarEmpProdFinanceiro?: boolean;
    telefone?: string;
    hashContatoNotificacao?: string;
    codigoVendedor?: number;
    porcentagemComissaoVenda?: number;
    pedidosSalao?: boolean;
    desconectarUsuarios?: boolean;
    removerTagsComanda?: boolean;
    acessaMenuVendas?: boolean;
    aceitaTermoUso?: boolean;
    dataAceitaTermoUso?: Date;
    percentualComissaoTaxaGarcom?: number;
    podeGerarExcelFinanceiro?: boolean;
    podeImprimirLancamentosFinanceiro?: boolean;
    gestorDeImpressoes?: boolean;
    lightTheme?: boolean;
    idioma?: string;
    twoAuthentication?: boolean;
    codeVerificacao?: string;
    usuarioLogado?: boolean;
}
